import { ReactNode, useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/router';
import QueryString from 'qs';
import { useTranslation } from 'react-i18next';
import { isArray } from 'lodash';
import { Input } from 'antd';
import { ILeadFilter } from '@Components/screens/telesales/LeadManager';

interface ChildOption {
  key: string;
  label: string;
  disabled?: boolean;
}

interface FilterOption {
  key: string;
  label: string | ReactNode;
  value: string[];
  badge: number;
  placeholder: string;
  children: ChildOption[];
  checked?: boolean;
  isActive?: boolean;
}

export default function useSelectOrderSourceData(
  types: any[],
  sources: any[],
) {
  const [searchValue, setSearchValue] = useState('');
  const { t: tFilter } = useTranslation('telesales');
  const [selectPageGroup, setSelectPageGroup] = useState<Map<string, string[]>>(
    new Map(),
  );

  const dataSelectOrderSource: FilterOption[] = useMemo(() => {
    const targetPageGroupPage = [
      {
        label: (
          <Input
            prefix={
              <img
                alt=""
                width={20}
                className="search_sale"
                src={`/icons/icon/Line/system/search-2-line.svg`}
              />
            }
            className="input-search_sale"
            onChange={value => {
              setSearchValue(value.target.value);
            }}
            autoComplete="off"
            autoFocus={true}
            id={`sub-menu-search`}
            placeholder={tFilter('Search...')}
          />
        ),
        key: `sub-menu-search`,
        badge: 0,
        children: [],
        placeholder: '',
        value: [],
      },
      {
        key: 'select-all',
        label: (
          <span>
            {selectPageGroup.size === types.length
              ? tFilter('Deselect All')
              : tFilter('Select All')}
          </span>
        ),
        value: [],
        badge: 0,
        placeholder: '',
        children: [],
        isSelectAll: true,
        isActive: true,
      },
    ] as FilterOption[];

    types.forEach(group => {
      let value = [];
      let badge = 0;
      let checked = false;
      let isActive = group.isActive;

      const pagesInGroup = sources
        .filter(page => page.type === group.id)
        .map(page => page.id);

      if (selectPageGroup.has(String(group.id))) {
        const valuePages = selectPageGroup.get(String(group.id));
        const validPages = valuePages.filter(pageId =>
          pagesInGroup.includes(pageId),
        );
        value = validPages;
        badge = validPages?.length || 0;
        checked = validPages.length > 0;
        isActive = group.isActive;
      }

      const childrenElements = sources
        .filter(page => page.type === group.id)
        .map(page => ({
          key: page.id,
          label: page.name,
        }));

      targetPageGroupPage.push({
        key: group.id,
        label: group.name,
        value: value,
        badge: badge,
        placeholder: 'Search pages',
        children: childrenElements,
        checked,
        isActive: group.isActive,
      });
    });

    if (searchValue) {
      return targetPageGroupPage.filter(ele => {
        if (ele.key === 'sub-menu-search') return true;
        if (typeof ele.label === 'string') {
          return ele.label.toLowerCase().includes(searchValue.toLowerCase());
        }
        return false;
      });
    }

    return targetPageGroupPage;
  }, [types, sources, selectPageGroup, searchValue, tFilter]);

  const memoTitle = useMemo(() => {
    const listGroupsChoose = [];
    const uniquesourceIds = new Set();

    for (const [groupId, sourceIds] of selectPageGroup) {
      const group = types.find(g => String(g.id) === String(groupId));
      if (group) {
        listGroupsChoose.push(group.name);
        sourceIds.forEach(id => uniquesourceIds.add(id));
      }
    }

    return [
      {
        name: 'source_type',
        value: listGroupsChoose.length > 0 ? listGroupsChoose.length : null,
        length: types.length,
      },
      { name: 'circle' },
      {
        name: 'source',
        value: uniquesourceIds.size > 0 ? uniquesourceIds.size : null,
        length: sources.length,
      },
    ];
  }, [types, sources, selectPageGroup]);

  return {
    dataSelectOrderSource,
    selectPageGroup,
    setSelectPageGroup,
    memoTitle,
    hasSelect: selectPageGroup.size > 0,
  };
}
