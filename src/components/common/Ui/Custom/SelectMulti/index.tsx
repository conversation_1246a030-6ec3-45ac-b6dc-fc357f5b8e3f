import {
  Dropdown,
  Input,
  MenuProps,
  Select,
  Space,
  Tooltip,
} from "antd";
import classnames from "classnames";
import {
  cloneDeep,
  filter,
  findIndex,
  isNull,
  lowerCase,
  sortBy,
} from "lodash";

import Style from "./select_multi.module.sass";
import { useEffect, useRef, useState } from "react";
import { ItemType, SubMenuType } from "antd/lib/menu/hooks/useItems";
import TextUtils from "@Utils/TextUtils";
import IconChecked from "@Components/common/Icons/Checked";
import { useTranslation } from "react-i18next";
import { Box } from "@chakra-ui/layout";
import classNames from "classnames";
import SvgRender from "@Components/common/Ui/SvgRender";
import TooltipOnOverflow from "@Components/common/Tooltip/TooltipOnOverflow";

type TitleType = {
  name?: string;
  value?: number | null;
  length?: number;
};

type DataSelectChildType = {
  label?: any;
  key?: string;
  active?: boolean;
  className?: any;
  disabled?: boolean;
};

type DataSelectType = {
  label?: any;
  placeholder?: string;
  badge?: number;
  key?: string;
  children?: DataSelectChildType[];
  value?: any[];
  checked?: boolean;
  isActive?: boolean;
  includeOutOfSession?: boolean;
};

type SelectMultiProps = {
  clear?: boolean;
  className: any;
  dropdownClassName: any;
  title?: TitleType[];
  data?: DataSelectType[];
  checkedColor?: string;
  onChangeData?: (type: string, item: any, isAll?: boolean, includeOutOfSession?: boolean) => void;
  clearFilter?: () => void;
  clearIcon?: string;
};

const dataGlobal = [];

const SelectMultiComponent = (props: SelectMultiProps) => {
  const { t: tDashboard } = useTranslation('dashboard', {
    keyPrefix: 'orderShipment.filter',
  });
  const { clear, clearFilter } = props;
  const [open, setOpen] = useState(false);
  const [data, setData] = useState<SubMenuType[] | ItemType[]>();
  const [searchResults, setSearchResults] = useState({});
  const TimeOut = useRef<any>(null);
  const clsContainer = classnames(
    Style.selectContainer,
    [props.className].filter(i => i),
  );
  const clsDropdown = classnames(
    Style.dropdownContainer,
    [props.dropdownClassName].filter(i => i),
  );
  useEffect(() => {
    const parseData =
      props?.data?.length > 0
        ? props?.data?.map((item: DataSelectType) => {
          dataGlobal[item?.key] = item?.children;
          return {
            ...item,
            label: (
              <div className={Style.label}>
                <span className={Style.colorBlack}>{item?.label}</span>
                {item?.badge >= 0 && (
                  <span className={classnames(Style.badge, Style.badgeLight)}>
                    {item?.badge}
                  </span>
                )}
              </div>
            ),
            children: parseDataSelect(item),
            popupClassName: Style.subMenuFilter,
          } as SubMenuType | ItemType;
        })
        : [{ label: "Not found data", key: "not-found-item" }];

    setData(prevData => {
      if (prevData !== parseData) {
        return parseData;
      } else {
        return prevData;
      }
    });
  }, [props?.data, searchResults]);

  const parseDataSelect = (item: DataSelectType) => {
    const filteredChildren = (searchResults[item.key] || item?.children || []).filter(
      (child: DataSelectChildType) => child.key !== "select-all" && child.key !== "select-all-source-type"
    );

    const isAllSelectedSourceType = filteredChildren.every((child: DataSelectChildType) =>
      child?.disabled ? true : item?.value?.includes(child?.key)
    );

    const isAllSelected = filteredChildren.every((child: DataSelectChildType) =>
      item?.value?.includes(child?.key)
    );

    const isSourceSelection = item.key === 'values';
    const isSourceTypeSelection = item.key === 'type';
    return [
      {
        label: (
          <Input
            placeholder={item?.placeholder}
            prefix={
              <img
                alt=""
                width={20}
                className="input-search"
                src={`/icons/icon/Line/system/search-2-line.svg`}
              />
            }
            className="input-search"
            onChange={value => handleSearch(value?.target?.value, item?.key)}
            autoComplete="off"
            autoFocus={true}
            id={`sub-menu-${item?.key}`}
          />
        ),
        key: `sub-menu-search-${item?.key}`,
        className: Style.topFilter,
      },
      {
        label: (
          <div className={Style.listData}>
            {isSourceSelection && (
              <div
                key="select-all"
                className={Style.itemData}
                onClick={() => handleSelectAll(item, filteredChildren, isAllSelected)}
              >
                <span>{tDashboard("Select All")}</span>
                {isAllSelected && (
                  <IconChecked width={20} height={20} fill={props?.checkedColor} />
                )}
              </div>

            )}
            {isSourceTypeSelection && (
              <div
                key="select-all-source-type"
                className={Style.itemData}
                onClick={() => handleSelectAllSourceType(item, filteredChildren, isAllSelectedSourceType)}
              >
                <span>{tDashboard("Select All")}</span>
                {isAllSelectedSourceType && (
                  <IconChecked width={20} height={20} fill={props?.checkedColor} />
                )}
              </div>
            )}
            {filteredChildren.map((child: DataSelectChildType) => (
              <div
                key={child.key}
                className={Style.itemData}
                onClick={() => {
                  if (child?.disabled) return;
                  handleItemClick(item, child);
                }}
                style={{ cursor: child?.disabled ? 'not-allowed' : 'pointer' }}
              >
                <span>{child.label}</span>
                {item?.value?.includes(child?.key) && (
                  <IconChecked width={20} height={20} fill={props?.checkedColor} />
                )}
              </div>
            ))}
          </div>
        ),
        key: `sub-menu-${item?.key}`,
        className: Style.boxData,
      },
    ];
  };

  const handleSelectAll = (item: DataSelectType, children: DataSelectChildType[], isAllSelected: boolean) => {
    const newValue = isAllSelected
      ? []
      : children.filter(child => !child.disabled).map(child => child.key);

    props?.onChangeData(item?.key, { key: "select-all", value: newValue });
  };
  const handleSelectAllSourceType = (item: DataSelectType, children: DataSelectChildType[], isAllSelectedSourceType: boolean) => {
    const newValue = isAllSelectedSourceType
      ? []
      : children.filter(child => !child.disabled).map(child => child.key);
    props?.onChangeData(item?.key, { key: "select-all-source-type", value: newValue });
  };

  const handleItemClick = (item: DataSelectType, child: DataSelectChildType) => {
    const newValue = item?.value?.includes(child?.key)
      ? item?.value?.filter(key => key !== child?.key)
      : [...(item?.value || []), child?.key];

    props?.onChangeData(item?.key, { key: child?.key, value: newValue });
  };
  const handleMenuClick: MenuProps["onClick"] = e => {
    if (e.key === "3") {
      setOpen(false);
    }
  };

  const handleSearch = (value: string, type: string) => {
    value = TextUtils.xoa_dau(lowerCase(value));

    if (TimeOut.current) clearTimeout(TimeOut.current);
    TimeOut.current = setTimeout(async () => {
      if (dataGlobal[type]) {
        const filterData = filter(
          dataGlobal[type],
          (o: DataSelectChildType) => {
            return (
              lowerCase(TextUtils.xoa_dau(lowerCase(o.label)))?.search(value) >
              -1 ||
              lowerCase(TextUtils.xoa_dau(lowerCase(o.key)))?.search(value) > -1
            );
          },
        );

        const indexData = findIndex(data, { key: type });

        if (indexData > -1) {
          const cloneData: any = cloneDeep(data);
          cloneData[indexData] = {
            ...cloneData[indexData],
            children: parseDataSelect({
              ...cloneData[indexData],
              children:
                filterData?.length > 0
                  ? filterData
                  : [{ label: "Not found data", key: "not-found-item" }],
            }),
          };
          setData(cloneData);
        }
        setSearchResults(prevSearchResults => ({ ...prevSearchResults, [type]: filterData }));
      }
    }, 500);
  };

  const handleOpenChange = (flag: boolean) => {
    setOpen(flag);
  };

  return (
    <div className={clsContainer}>
      <Dropdown
        trigger={["click"]}
        className={clsDropdown}
        overlayClassName={Style.overlayContainer}
        menu={{
          items: (data as ItemType[]) ?? [],
          onClick: handleMenuClick,
          triggerSubMenuAction: "hover",
          multiple: true,
          subMenuOpenDelay: 0.25,
          onOpenChange: value => {
            if (value?.[0]) {
              setTimeout(() => {
                const ele: any = document.getElementById(
                  `sub-menu-${value?.[0]}`,
                );
                ele.focus();
              }, 500);
            }
          },
        }}
        onOpenChange={handleOpenChange}
        open={open}
      >
        <a onClick={e => e.preventDefault()}>
          <Space>
            {props?.title?.map((item: TitleType) => {
              if (item?.name == "circle")
                return <div key={item?.name} className={Style.circle}></div>;
              return (
                <div
                  key={item?.name}
                  className={classnames(Style.label, {
                    [Style.colorSecondary]: isNull(item?.value),
                  })}
                >
                  <span className={classnames(Style.colorBlack, Style.title)}>
                    {item?.name}
                  </span>
                  {!isNull(item?.value) &&
                    item?.value >= 0 &&
                    item?.length != item?.value && (
                      <span className={Style.badge}>{item?.value}</span>
                    )}
                </div>
              );
            })}
            {clear ? (
              <img
                src="/icons/icons/Close Circle/Filled.svg"
                alt=""
                width={15}
                onClick={() => clearFilter()}
              />
            ) : (
              <img
                src="/icons/icon/Line/Arrow/arrow-down-s-line.svg"
                alt=""
                width={15}
              />
            )}
          </Space>
        </a>
      </Dropdown>
    </div>
  );
};

export const SelectMultiComponentSale = (props: SelectMultiProps & {
  hasSelect: boolean,
  orderText: any
}) => {
  const { clear, clearFilter, clearIcon } = props;
  const [open, setOpen] = useState(false);
  const [data, setData] = useState<SubMenuType[] | ItemType[]>();
  const [searchResults, setSearchResults] = useState<Record<string, DataSelectChildType[]>>({});
  const [isSearching, setIsSearching] = useState<Record<string, boolean>>({});
  const TimeOut = useRef<any>(null);
  const { t: tFilter } = useTranslation("dashboard");
  const clsContainer = classnames(
    Style.selectContainer,
    [props.className].filter(i => i),
  );
  const clsDropdown = classnames(
    Style.dropdownContainer,
    Style.pl16,
    [props.dropdownClassName].filter(i => i),
  );

  const handleMenuClick: MenuProps["onClick"] = e => {
    if (e.key === "3") {
      setOpen(false);
    }
  };

  const handleItemClick = (item: DataSelectType, child: DataSelectChildType) => {
    const newValue = item?.value?.includes(child?.key)
      ? item?.value?.filter(key => key !== child?.key)
      : [...(item?.value || []), child?.key];

    props?.onChangeData(item?.key, { key: child?.key, value: newValue });
  };

  const handleOpenChange = (flag: boolean) => {
    setOpen(flag);
    if (!flag) {
      setActiveSubMenu(null);
    }
  };

  const handleSearch = (value: string, type: string) => {
    setIsSearching(prev => ({
      ...prev,
      [type]: value.trim().length > 0,
    }));

    const searchValue = value.trim();

    if (TimeOut.current) clearTimeout(TimeOut.current);
    TimeOut.current = setTimeout(async () => {
      if (dataGlobal[type]) {
        const filterData = filter(
          dataGlobal[type],
          (o: DataSelectChildType) => {
            const normalizedSearchValue = TextUtils.xoa_dau(lowerCase(searchValue));
            const normalizedLabel = TextUtils.xoa_dau(lowerCase(String(o.label || '')));
            const keyString = String(o.key || '');
            return (
              keyString.includes(searchValue) ||
              normalizedLabel.includes(normalizedSearchValue)
            );
          },
        );

        const sortedData = sortBy(filterData, (o: DataSelectChildType) => {
          const keyString = String(o.key || '');
          return keyString.includes(searchValue) ? 0 : 1;
        });

        setSearchResults(prevSearchResults => ({ ...prevSearchResults, [type]: sortedData }));
      }
    }, 500);
  };

  const [activeSubMenu, setActiveSubMenu] = useState<string | null>(null);

  const parseDataSelect = (item: DataSelectType) => {
    if (item.children.length == 0) {
      return [];
    }
    const filteredChildren = (searchResults[item.key] || item?.children || []);
    const isCurrentlySearching = isSearching[item.key] || false;

    return [
      {
        label: (
          <Input
            placeholder={tFilter("Search...")}
            prefix={
              <img
                alt=""
                width={20}
                className="input-search"
                src={`/icons/icon/Line/system/search-2-line.svg`}
              />
            }
            className="input-search"
            onChange={value => handleSearch(value?.target?.value, item?.key)}
            autoComplete="off"
            autoFocus={true}
            id={`sub-menu-${item?.key}`}
          />
        ),
        key: `sub-menu-search-${item?.key}`,
        className: Style.topFilter,
      },
      {
        label: (
          <div className={Style.listData}>
            {!isCurrentlySearching && (
              <Box
                key="select-all-source-type"
                className={Style.itemData}
                sx={{
                  gap: '8px',
                }}
                onClick={() => {
                  props?.onChangeData(
                    item?.key,
                    {
                      key: -99,
                      value: -99,
                    },
                    item.badge == (filteredChildren.length)
                  );
                }}
              >
                <span>
                  {item?.badge === filteredChildren.length ? tFilter("Deselect All") : tFilter("Select All")}
                </span>
              </Box>
            )}
            {filteredChildren
              .map((child: DataSelectChildType) => (
                <Box
                  sx={{
                    gap: '8px',
                    maxWidth: '216px',
                    minWidth: '191px',
                  }}
                  key={child.key}
                  className={Style.itemData}
                  onClick={() => {
                    if (child?.disabled) return;
                    handleItemClick(item, child);
                  }}
                  style={{ cursor: child?.disabled ? 'not-allowed' : 'pointer' }}
                >
                  <TooltipOnOverflow text={child.label} />
                  {item?.value?.includes(child?.key) && (
                    <Box sx={{
                      minWidth: '20px',
                      height: '20px',
                    }}>
                      <IconChecked width={20} height={20} fill={props?.checkedColor} />
                    </Box>
                  )}
                </Box>
              ))}
          </div>
        ),
        key: `sub-menu-${item?.key}`,
        className: Style.boxData,
      },
    ];
  };

  useEffect(() => {
    const parseData =
      props?.data?.length > 0
        ? props?.data?.map((item: DataSelectType) => {
          dataGlobal[item?.key] = item?.children;
          if (item.key == 'sub-menu-search') {
            return {
              ...item,
              children: [],
              className: 'sub-menu-search-ffm',
              popupClassName: Style.subMenuFilter,
            } as SubMenuType | ItemType;
          }
          return {
            ...item,
            label: (
              <Box onClick={() => {
                props?.onChangeData(item?.key, null);
              }} sx={{
                justifyContent: 'space-between',
              }} className={Style.label}>
                <Tooltip title={item?.label} className={Style.label} placement="topLeft">
                  <div style={{ textOverflow: "ellipsis", overflow: "hidden", whiteSpace: "nowrap", maxWidth: 200 }}>{item?.label}</div>
                </Tooltip>
                {!item.isActive && (
                  <span style={{ color: "#D0D5DD", marginLeft: 5, fontSize: 12, marginBottom: -2 }}>{tFilter("Deactivated")}</span>
                )}
                {item?.badge > 0 && (
                  <Box sx={{
                    minWidth: '19px',
                    height: '16px',
                  }} className={classnames(Style.badge, Style.badgeLight)}>
                    {item?.badge}
                  </Box>
                )}
              </Box>
            ),
            expandIcon: (item?.checked) ? <IconChecked width={20} height={20} fill={"#07B2A9"} /> : (item?.children?.length > 0 ? <SvgRender height={20} width={20} src="icon/Line/Arrow/arrow-drop-right-line" color='black' /> : <div />),
            children: parseDataSelect(item),
            className: 'sub-menu-item-ffm',
            popupClassName: Style.newMenuFilter,
          } as SubMenuType | ItemType;
        })
        : [{ label: "Not found data", key: "not-found-item" }];
    setData(prevData => {
      if (prevData !== parseData) {
        return parseData;
      } else {
        return prevData;
      }
    });
  }, [props?.data, tFilter, searchResults]);
  return (
    <div className={clsContainer}>
      <Dropdown
        trigger={["click"]}
        className={clsDropdown}
        overlayClassName={classnames(Style.overlayContainer, Style.mergedOverlay)}
        menu={{
          items: (data as ItemType[]) ?? [],
          onClick: handleMenuClick,
          triggerSubMenuAction: "click",
          multiple: true,
          subMenuOpenDelay: 0.25,
          onOpenChange: (openKeys) => {
            if (openKeys.length > 0) {
              const latestOpenKey = openKeys[openKeys.length - 1];
              if (latestOpenKey !== activeSubMenu) {
                setActiveSubMenu(latestOpenKey);
              }
            }
          },
        }}
        onOpenChange={handleOpenChange}
        open={open}
      >
        <a onClick={e => e.preventDefault()}>
          <Box sx={{
            display: 'inline-flex',
            gridTemplateColumns: '1fr auto 1fr auto',
            gap: '8px',
            alignItems: 'center',
            textOverflow: 'ellipsis',
            overflow: 'hidden',
            whiteSpace: 'nowrap',
          }}>
            {props?.title?.map((item: TitleType) => {
              if (item?.name == "circle")
                return <div key={item?.name} className={Style.circle}></div>;
              return (
                <div
                  style={{
                    flex: 1,
                  }}
                  key={item?.name}
                  className={classnames(Style.label, {
                    [Style.colorBlack]: isNull(item?.value),
                  })}
                >
                  <span className={classnames(Style.colorBlack)}>
                    {tFilter(item?.name)}
                  </span>
                  {!isNull(item?.value) &&
                    item?.value >= 0 &&
                    (
                      <span className={Style.badge}>{item?.value}</span>
                    )}
                </div>
              );
            })}
            {clear && props.hasSelect ? (
              <img
                src={ 
                  clearIcon ? clearIcon : '/icons/icons/Close Circle/Filled.svg'
                }
                alt=""
                width={15}
                onClick={(e) => {
                  e.stopPropagation();
                  clearFilter();
                }}
              />
            ) : (
              <img
                src="/icons/icon/Line/Arrow/arrow-down-s-line.svg"
                alt=""
                width={15}
              />
            )}
          </Box>
        </a>
      </Dropdown>
    </div>
  );
};
export const SelectMultiComponentNormal = (props: SelectMultiProps & {
  hasSelect: boolean,
  orderText: any,
  current: any
}) => {
  const { clear, clearFilter, current } = props;
  const [open, setOpen] = useState(false);
  const [data, setData] = useState<SubMenuType[] | ItemType[]>();
  const clsContainer = classnames(
    Style.selectContainer,
    [props.className].filter(i => i),
  );
  const clsDropdown = classnames(
    Style.dropdownContainer,
    Style.pl16,
    [props.dropdownClassName].filter(i => i),
  );

  const handleMenuClick: MenuProps["onClick"] = e => {
    if (e.key === "3") {
      setOpen(false);
    }
  };

  const handleItemClick = (item: DataSelectType, child: DataSelectChildType) => {
    const newValue = item?.value?.includes(child?.key)
      ? item?.value?.filter(key => key !== child?.key)
      : [...(item?.value || []), child?.key];

    props?.onChangeData(item?.key, { key: child?.key, value: newValue });
  };

  const handleOpenChange = (flag: boolean) => {
    setOpen(flag);
    const ele = document.getElementById(
      `sub-menu-search`,
    ) as HTMLInputElement;
    if (ele) {
      setTimeout(() => {
        ele.focus();
      }, 200);
    }
    console.log("handleOpenChange", ele);
  };

  const parseDataSelect = (item: DataSelectType) => {
    if (item.children.length == 0) {
      return [];
    }
    return [
      {
        label: (
          <div className={Style.listData}>
            <Box
              key="select-all-source-type"
              className={Style.itemData}
              sx={{
                gap: '8px',
              }}
              onClick={() => {
                props?.onChangeData(
                  item?.key,
                  {
                    key: -99,
                    value: -99,
                  },
                  (item.badge + (item.includeOutOfSession ? 1 : 0)) == (item.children.length + 1)
                );
              }}
            >
              <span>{props.orderText("export.all")}</span>
              {(item.badge + (item.includeOutOfSession ? 1 : 0)) == (item.children.length + 1) && (
                <IconChecked width={20} height={20} fill={props?.checkedColor} />
              )}
            </Box>
            {item.children.map((child: DataSelectChildType) => (
              <Box
                sx={{
                  gap: '8px',
                  maxWidth: '191px',
                  minWidth: '191px'
                }}
                key={child.key}
                className={Style.itemData}
                onClick={() => {
                  if (child?.disabled) return;
                  handleItemClick(item, child);
                }}
                style={{ cursor: child?.disabled ? 'not-allowed' : 'pointer' }}
              >
                <TooltipOnOverflow placement="leftBottom" text={child.label} />
                {item?.value?.includes(child?.key) && (
                  <Box sx={{
                    minWidth: '20px',
                    height: '20px',
                  }}>
                    <IconChecked width={20} height={20} fill={props?.checkedColor} />
                  </Box>
                )}
              </Box>
            ))}
            <Box
              key="select-all-source-type"
              className={Style.itemData}
              sx={{
                gap: '8px'
              }}
              onClick={() => {
                props?.onChangeData(
                  item?.key,
                  {
                    key: -9999,
                    value: -9999,
                  },
                  undefined,
                  item.includeOutOfSession
                );
              }}
            >
              <span>{props.orderText("label.out_of_session")}</span>
              {item.includeOutOfSession && (
                <IconChecked width={20} height={20} fill={props?.checkedColor} />
              )}
            </Box>
          </div>
        ),
        key: `sub-menu-${item?.key}`,
        className: Style.boxData,
      },
    ];
  };
  useEffect(() => {
    const parseData =
      props?.data?.length > 0
        ? props?.data?.map((item: DataSelectType) => {
          dataGlobal[item?.key] = item?.children;
          if (item.key == 'sub-menu-search') {
            return {
              ...item,
              children: [],
              className: 'sub-menu-search-ffm',
              popupClassName: Style.subMenuFilter,
            } as SubMenuType | ItemType;
          }
          return {
            ...item,
            label: (
              <Box onClick={() => {
                props?.onChangeData(item?.key, null);
              }} sx={{
                justifyContent: 'space-between',
              }} className={Style.label}>
                <TooltipOnOverflow placement="leftBottom" className={classNames(Style.colorBlack, Style.maxWidth245)} text={item?.label} />
                {(item?.badge ?? 0) + (item?.includeOutOfSession ? 1 : 0) > 0 && (
                  <Box sx={{
                    minWidth: '19px',
                    height: '16px'
                  }} className={classnames(Style.badge, Style.badgeLight)}>
                    {(item?.badge ?? 0) + (item?.includeOutOfSession ? 1 : 0)}
                  </Box>
                )}
              </Box>
            ),
            expandIcon: (item?.checked) ? <IconChecked width={20} height={20} fill={"#BD2026"} /> : (item?.children?.length > 0 ? <SvgRender height={20} width={20} src="icon/Line/Arrow/arrow-drop-right-line" color='black' /> : <div />),
            children: parseDataSelect(item),
            className: 'sub-menu-item-ffm',
            popupClassName: Style.newMenuFilter,
          } as SubMenuType | ItemType;
        })
        : [{ label: "Not found data", key: "not-found-item" }];
    setData(prevData => {
      if (prevData !== parseData) {
        return parseData;
      } else {
        return prevData;
      }
    });
  }, [props?.data, current]);
  return (
    <div className={clsContainer}>
      <Dropdown
        trigger={["click"]}
        className={clsDropdown}
        overlayClassName={classnames(Style.overlayContainer, Style.mergedOverlay)}
        menu={{
          items: (data as ItemType[]) ?? [],
          onClick: handleMenuClick,
          triggerSubMenuAction: "hover",
          multiple: true,
          subMenuOpenDelay: 0.25,
        }}
        onOpenChange={handleOpenChange}
        open={open}
      >
        <a onClick={e => e.preventDefault()}>
          <Box sx={{
            display: 'grid',
            gridTemplateColumns: '1fr auto 1fr auto',
            gap: '8px',
            maxWidth: '245px',
            minWidth: '245px',
            alignItems: 'center',
          }}>
            {props?.title?.map((item: TitleType) => {
              if (item?.name == "circle")
                return <div key={item?.name} className={Style.circle}></div>;
              return (
                <div
                  style={{
                    flex: 1,
                  }}
                  key={item?.name}
                  className={classnames(Style.label, {
                    [Style.colorSecondary]: isNull(item?.value),
                  })}
                >
                  <span className={classnames(Style.colorBlack)}>
                    {item?.name}
                  </span>
                  {!isNull(item?.value) &&
                    item?.value >= 0 &&
                    item?.length != item?.value && (
                      <span className={Style.badge}>{item?.value}</span>
                    )}
                </div>
              );
            })}
            {clear && props.hasSelect ? (
              <img
                src="/icons/icons/Close Circle/Filled.svg"
                alt=""
                width={15}
                onClick={(e) => {
                  e.stopPropagation();
                  clearFilter();
                }}
              />
            ) : (
              <img
                src="/icons/icon/Line/Arrow/arrow-down-s-line.svg"
                alt=""
                width={15}
              />
            )}
          </Box>
        </a>
      </Dropdown>
    </div>
  );
};
SelectMultiComponent.Option = Select.Option;

export default SelectMultiComponent;
