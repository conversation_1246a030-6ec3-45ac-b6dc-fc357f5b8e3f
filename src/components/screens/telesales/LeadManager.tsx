import HeaderPosition from '@Components/common/Layout/HeaderPosition';
import ActionButton from '@Components/common/Ui/Custom/ActionButton';
import RenderProduct from '@Components/common/Ui/RenderProduct';
import SvgRender from '@Components/common/Ui/SvgRender';
import OrderRepository from '@Repository/order/OrderRepository';
import {
  PageProps,
  ProjectType,
  UseSelectorType,
} from '@Type/common.interface';
import { Lead, Schedule } from '@Type/order-api.interface';
import PriceUtils from '@Utils/PriceUtils';
import TextUtils from '@Utils/TextUtils';
import { Box, Flex, HStack } from '@chakra-ui/layout';
import {
  Badge,
  Cascader,
  Drawer,
  Dropdown,
  Input,
  message,
  Pagination,
  Popover,
  Segmented,
  Space,
  Table,
  Tag,
  TagType,
  Tooltip,
} from 'antd';
import axios, { CancelTokenSource } from 'axios';
import {
  cloneDeep,
  intersection,
  isEmpty,
  isNil,
  omit,
  orderBy,
  reduce,
  sumBy,
  uniqBy,
} from 'lodash';
import moment from 'moment';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import {
  CareState,
  CareStateConfig,
  GroupStateManager,
  SourceEntity,
} from 'src/enum/order-api.enum';
import styled from 'styled-components';
import AppointmentSchedule from './components/AppointmentSchedule';
import LeadAdvancedFilter from './components/LeadAdvancedFilter';
import LeadTab from './components/LeadTab';
import { LeadContextProvider } from './context/LeadContext';
import LeadDetail from './lead-detail/LeadDetail';
import { useProjectAvailable } from '../../../hooks/useProjectActiveAvailable';
import { useHasPermission } from '../../../hooks/useUser';
import { SalePermission } from '../../../enum/sale-permission';
import { TelesalesPermission } from '../../../enum/sale-permission/telesales-permission.enum';
import { getUsersByModuleIncharge } from '@Utils/funcs';
import { ModuleInCharge } from '@Components/screens/settings/v2/role-permission/RolePermissionDetail';
import { useFetchAllUserHasProfiles } from '../../../hooks/useFetchUserInfo';
import { useIsManager } from '../../../hooks/commonFuncs';
import AGSaleRangePicker from '@Components/common/AGRangePicker';
import { PhoneNumberPrefix } from '@Utils/Constants';
import { useQuery } from '@tanstack/react-query';
import UserRepository from '@Repository/user/UserRepository';
import CallButton from '@Components/screens/telesales/components/CallButton';
import AgSalerCensorComponent, {
  maskText,
  maskTextPhone,
} from '@Components/common/Ui/AGSalerCensorComponent';
import { OrderStatusEnum } from '@Components/screens/orders/const/order.tab.enum';
import { a } from '@firebase/firestore/dist/node-cjs/packages/firestore/dist/node-esm2017/database-502408a4';
import { useYCall } from '@Components/common/Layout/Layout';
import CallButtonWithYCall from './components/CallButtonWithYCall';
import IconCommentLine from '@Components/common/Icons/IconCommentLine';
import { useRouter } from 'next/router';
import TimeIcon from '@Components/common/Icons/TimeIcon';
import UpIcon from '@Components/common/Icons/UpIcon';
import classNames from 'classnames';
import AGMultiSelectWithTab from '@Components/common/Ui/AGMultiSelectWithTab';
import { queryClient } from 'src/providers/TanstackQueryProvider';
import AGMultiSelect from '@Components/common/Ui/AGMultiSelect';
import ProductRepository from '@Repository/product/ProductRepository';
import { SelectMultiComponentSale } from '@Components/common/Ui/Custom/SelectMulti';
import useSelectParentWithChild from '@Components/common/hooks/useOrderSource';
import { TelesalesSources } from 'src/constants/orders.contants';
import AutoInboxRepository from '@Repository/page/AutoInboxRepository';

type IProps = PageProps;

export enum UserDeviceCallState {
  CALL_STATE_IDLE = 'CALL_STATE_IDLE',
  CALL_STATE_RINGING = 'CALL_STATE_RINGING',
  CALL_STATE_OFFHOOK = 'CALL_STATE_OFFHOOK',
  OFFLINE = 'OFFLINE',
}

export interface ILeadFilter {
  query?: string;
  fromCurrentCare?: number;
  toCurrentCare?: number;
  page?: number;
  limit?: number;
  state?: CareState[];
  userIds?: number[];
  excludeUserIds?: number[];
  usedToBeAssigned?: boolean;
  sourceType?: SourceEntity;
  sourceIds?: any[];
  projectIds?: number[];
  reasonIds?: any[];
  productIds?: any[];
  sort?: string;
  orderBy?: string;
  reasonTab?: string;
}

export interface internalFilter {
  page: number;
  limit: number;
}

export interface quickFilter {
  page?: number;
  limit?: number;
  state?: string[];
}

const dateFormat = 'DD/MM/YYYY';

const LeadManager = () => {
  const { t: tMenu } = useTranslation('menu');
  const { t: tMess } = useTranslation('message');
  const UserLogin = useSelector((state: UseSelectorType) => state.user);
  const isTelesalesManager = useIsManager([ModuleInCharge.telesale]);

  const projectAvailable = useProjectAvailable();
  const projectActiveAvailable = projectAvailable.filter(
    (project: ProjectType) => {
      return localStorage
        .getItem('projectActive')
        ?.split(',')
        .map(it => Number(it))
        .includes(Number(project.id));
    },
  );

  const projectIds = projectActiveAvailable?.map(project => project?.id);
  const router = useRouter();
  const queryUrl = router.query;
  const { tab = 'assigned' } = queryUrl as {
    tab: 'assigned' | 'in_process' | 'confirmed' | 'failed';
  };
  const { seg = 'all' } = queryUrl as { seg: string };
  const { t: transTelesales } = useTranslation('telesales');
  const { t: tOrder } = useTranslation('order');
  const { i18n } = useTranslation();
  moment.locale(i18n.language);
  const [query, setQuery] = useState('');
  const [segmented, setSegmented] = useState(seg);
  const [loading, setLoading] = useState(false);
  const [telesales, setTelesales] = useState([]);
  const [data, setData] = useState([]);
  const [totalRows, setTotalRows] = useState(0);
  const [dataCount, setDataCount] = useState([]);
  const [userReduce, setUserReduce] = useState({});
  const [schedules, setSchedules] = useState<Schedule[]>([]);
  const [leadDetail, setLeadDetail] = useState<Lead>();
  const [visibleLeadDetail, setVisibleLeadDetail] = useState<boolean>(false);
  const [landings, setLandings] = useState([]);
  const [visibleAdvancedFilter, setVisibleAdvancedFilter] = useState(false);
  const [visibleCall, setVisibleCall] = useState(false);
  const [leadCall, setLeadCall] = useState<Lead>();
  const [params, setParams] = useState({});
  const [openSortPopup, setOpenSortPopup] = useState(false);
  const [pages, setPages] = useState([]);
  const ref = useRef(null);

  const { YCallStatus, setYCallStatus } = useYCall();

  const callStateRef = useRef(UserDeviceCallState.CALL_STATE_IDLE);
  const [tabActive, setTabActive] = useState<
    'assigned' | 'in_process' | 'confirmed' | 'failed'
  >(tab);
  const [_fetching, setFetching] = useState(false);
  const [filter, setFilter] = useState<ILeadFilter>({
    page: 1,
    limit: 20,
    state:
      seg === 'all'
        ? (GroupStateManager.find(it => it.key === tab)?.state as CareState[])
        : [seg as CareState],
    userIds: isTelesalesManager ? [] : [UserLogin.id],
    excludeUserIds: undefined,
    usedToBeAssigned: undefined,
    projectIds,
    fromCurrentCare: moment().subtract(7, 'days').startOf('days').valueOf(),
    toCurrentCare: moment().endOf('days').valueOf(),
  });

  const [internalFilter, setInternalFilter] = useState({
    page: 1,
    limit: 500,
  });

  const orderSourceType = Object.values(TelesalesSources).map((item, index) => {
    return {
      id: item.value,
      name: item.label,
      isActive: true,
    };
  });

  const sourceAll = useMemo(() => {
    return [
      ...landings.map(e => {
        return {
          ...e,
          type: SourceEntity.landing_page,
        };
      }),
      ...pages.map(e => {
        return {
          ...e,
          type: SourceEntity.fb_page,
        };
      }),
    ];
  }, [landings, pages]);

  const {
    dataSelectOrderSource,
    selectPageGroup,
    setSelectPageGroup,
    memoTitle,
    hasSelect,
  } = useSelectParentWithChild(orderSourceType, sourceAll);

  const CancelTokenStatic = axios.CancelToken;
  const _cancelToken = useRef<CancelTokenSource>();

  const loadMoreRef = useRef(false);
  const loadDoneRef = useRef(false);
  const currentPageRef = useRef(filter.page);
  const dataRef = useRef(data);
  // useEffect(() => {
  //   _fetchDetail();
  // }, [data]);
  // const _fetchDetail = async () => {
  //   if (!orderIds) return;
  //   const promises = orderIds.map(orderId => OrderRepository.getDetail(orderId));
  //   const results = await Promise.all(promises);
  //   const waybills = results.map(({ response, error }) => response?.data?.carrier?.waybillNumber);
  //   setWaybill(waybills);
  // };
  const initialFetchCount = useRef(0);

  useEffect(() => {
    moment.locale(i18n.language);
    queryClient.refetchQueries(['fetch-care-reasons-leads-manager']);
  }, [i18n.language]);

  useEffect(() => {
    _fetchSchedules();
    _fetchDataCount();
    _fetchLandings();
    _fetchPages();
    fetchYCallStatus();
    // fetchSipConfig()
    router.push({
      query: { tab: tabActive, seg },
    });
  }, []);

  useEffect(() => {
    _fetchLeads();
    if (initialFetchCount.current < 2) {
      initialFetchCount.current += 1;
      return;
    }
    _fetchDataCount();
  }, [filter]);

  useEffect(() => {
    currentPageRef.current =
      (internalFilter.limit * (internalFilter.page - 1)) / filter.limit + 1;
    setFilter({
      ...filter,
      page: currentPageRef.current,
    });
  }, [internalFilter]);

  const handleScroll = e => {
    const { target } = e;
    // if (target.scrollHeight - target.scrollTop === target.clientHeight) {
    //   _fetchMoreLeads()
    // }
    if (target.scrollTop + target.clientHeight > target.scrollHeight - 200) {
      _fetchMoreLeads();
    }
  };
  useEffect(() => {
    const tableBody = document.querySelector('.ant-table-body');
    tableBody.addEventListener('scroll', handleScroll);
    return () => {
      tableBody.removeEventListener('scroll', handleScroll); // Clean up on unmount
    };
  }, [filter]);

  const _onChangeDataDetailSuccess = () => {
    _fetchDataCount();
    _fetchLeads();
  };

  const { data: devices = [] } = useQuery({
    queryKey: ['fetch-user-devices'],
    queryFn: async () => {
      const { response } = await UserRepository.getUserDevices();
      return (
        response?.data.filter(
          x =>
            x.status !== UserDeviceCallState.OFFLINE && x?.simSlot?.length > 0,
        ) || []
      );
    },
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });

  // const _fetchSource = async () => {
  //   const { response, error } = await OrderRepository.getLandings({
  //     page:1,
  //     limit: 1000
  //   });
  //   if (response && response?.data) {
  //     setSources(response?.data);
  //   }
  // };

  // const _fetchPages = async () => {
  //   const { response, error } = await AutoInboxRepository.getData({
  //     page: 1,
  //     limit: 10000,
  //   });
  //   if (response && response?.data) {
  //     setPages(
  //       response?.data?.map(it => ({ ...it, name: `${it.name} - ${it.id}` })),
  //     );
  //   }
  // };

  const _fetchLandings = async () => {
    const { response, error } = await OrderRepository.getLandings({
      page: 1,
      limit: 10000,
    });
    if (response && response?.data) {
      setLandings(response?.data);
    }
  };

  const _fetchPages = async () => {
    const { response, error } = await AutoInboxRepository.getData({
      page: 1,
      limit: 10000,
    });
    if (response && response?.data) {
      setPages(response?.data);
    }
  };
  const hasApointmentPerm = useHasPermission(SalePermission.telesales, [
    TelesalesPermission.appointments,
  ]);

  const _fetchSchedules = async () => {
    if (!hasApointmentPerm) return;
    const { response, error } = await OrderRepository.getSchedule({
      leadUserIds: filter.userIds,
      excludeLeadUserIds: filter.excludeUserIds,
      usedToBeAssigned: filter.usedToBeAssigned,
    });
    if (response?.data) {
      setSchedules(response?.data);
    } else {
      message.error(error?.error_description);
    }
  };

  const paginationChange = (page: number, limit: number) => {
    setInternalFilter({
      page,
      limit,
    });
    _refresh();
  };
  const _onChangeDateHandOver = (date: any) => {
    if (filter) {
      const _filter = cloneDeep(filter);
      if (!date) {
        delete _filter.fromCurrentCare;
        delete _filter.toCurrentCare;
      } else {
        (_filter.fromCurrentCare = date?.[0]
          ? moment(date[0]).startOf('minutes').valueOf()
          : null),
          (_filter.toCurrentCare = date?.[1]
            ? moment(date[1]).endOf('minutes').valueOf()
            : null);
      }
      setFilter(_filter);
    }
  };
  const { data: usersQuery } = useFetchAllUserHasProfiles();

  useEffect(() => {
    if (usersQuery) {
      setTelesales(
        getUsersByModuleIncharge(usersQuery, [ModuleInCharge.telesale]),
      );
      setUserReduce(
        reduce(
          usersQuery,
          (pre: any, next: any) => {
            pre[next.id] = next;
            return pre;
          },
          {},
        ),
      );
    }
  }, [usersQuery]);

  const { data: careReasons = [] } = useQuery({
    queryKey: ['fetch-care-reasons-leads-manager'],
    queryFn: async () => {
      const { response } = await OrderRepository.getCareReasons();
      if (response && response?.data) {
        return [
          {
            id: -1,
            name: transTelesales(`reason.blank`),
          },
          ...response?.data?.map((it: any) => ({
            ...it,
            name: transTelesales(`reason.${it.reasonKey}`),
          })),
        ];
      }
    },
  });

  const { data: products = [] } = useQuery({
    queryKey: ['fetch-products-leads-manager', projectIds],
    queryFn: async () => {
      const { response } = await ProductRepository.getData({
        page: 1,
        limit: 2000,
        projectIds,
      });
      if (response?.data) {
        const newVariant: any = [];
        response?.data?.map((product: any) => {
          product.variations?.map((variant: any) => {
            if (variant.status !== -99) {
              newVariant.push({
                id: variant.id,
                name: `${product?.name} ${
                  variant?.sku ? `(${variant?.sku})` : ''
                }`,
                projectId: product.projectId,
              });
            }
          });
        });
        return newVariant;
      }
    },
  });

  const _fetchDataCount = async () => {
    const { response, error } = await OrderRepository.getLeadCount(
      omit({ ...filter, timestamp: moment().valueOf() }, [
        'page',
        'limit',
        'state',
      ]),
    );
    if (response?.data) {
      setDataCount(response?.data);
      setTotalRows(sumBy(response?.data, (it: any) => Number(it.count)));
    } else {
      setDataCount([]);
      setTotalRows(0);
    }
  };
  const _fetchLeads = async () => {
    if (_cancelToken.current) {
      _cancelToken.current.cancel();
      _cancelToken.current = undefined;
    }

    _cancelToken.current = CancelTokenStatic.source();
    setLoading(true);
    const { response, error } = await OrderRepository.getLeads(
      {
        ...filter,
        getAllAppointments: true,
        getUpcomingAppointments: true,
        timestamp: moment().valueOf(),
        getExternalSource: true,
      },
      _cancelToken.current!.token,
    );
    _cancelToken.current = undefined;
    setLoading(false);
    if (response?.data) {
      dataRef.current = response.data;
      setData(dataRef.current);
      setTotalRows(response?.count);
    }
  };
  const _fetchMoreLeads = async () => {
    if (
      currentPageRef.current + 1 >
      (internalFilter.limit * (internalFilter.page - 1)) / filter.limit + 25
    )
      return;
    if (loadMoreRef.current) return;
    if (loadDoneRef.current) return;
    loadMoreRef.current = true;
    currentPageRef.current += 1;
    const { response, error } = await OrderRepository.getLeads(
      {
        ...filter,
        state: filter.state,
        page: currentPageRef.current,
        getAllAppointments: true,
        getUpcomingAppointments: true,
        timestamp: moment().valueOf(),
        getExternalSource: true,
      },
      // _cancelToken.current!.token
    );
    loadMoreRef.current = false;
    if (response?.data) {
      if (response?.data.length === 0) loadDoneRef.current = true;
      dataRef.current = [...dataRef.current, ...response.data];
      setData(dataRef.current);
      setTotalRows(response?.count);
    }
  };

  const fetchYCallStatus = async () => {
    try {
      const res = await OrderRepository.getCallCenterStatus('ycall', params);
      if (res?.response?.data?.isActive) {
        setYCallStatus(res?.response?.data?.isActive);
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };

  const _onKeyPress = event => {
    if (event.key === 'Enter') {
      setFilter({
        ...filter,
        query,
      });
    }
  };

  const onCellClick = (record: any) => {
    return {
      onClick: () => {
        window.open(`/telesales/leads-management/${record.id}`, '_blank');
      },
    };
  };

  const _takeAssignedLead = async id => {
    setLoading(true);
    const { response, error } = await OrderRepository.takeAssignedLead(id);

    if (response?.data) {
      _fetchLeads();
      _fetchDataCount();
      message.success(transTelesales('common.successfully'));
      setLoading(false);
    } else {
      setLoading(false);
      message.error(
        error?.response?.data?.code === 'TLS_0003'
          ? tMess('TLS_0003')
          : error?.error_description,
      );
    }
  };

  const handleClickOutside = e => {
    if (ref.current && !ref.current.contains(e.target)) {
      setOpenSortPopup(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const updateFilterWithSelected = (selectedMap: Map<string, string[]>) => {
    const sourceTypes = [];
    const sourceIds = [];

    for (const [groupId, pages] of selectedMap) {
      sourceTypes.push(groupId);
      sourceIds.push(...pages);
    }
    const newFilter = {
      ...filter,
      sourceTypes: sourceTypes,
      sourceIds: sourceIds,
    };
    setFilter(newFilter);
  };

  const mockData = {
    data: dataSelectOrderSource,
    title: memoTitle,
    clear: true,
    clearFilter: () => {
      setSelectPageGroup(new Map());
      const newFilter = {
        ...filter,
      };

      delete (newFilter as any)['sourceTypes'];
      delete (newFilter as any)['sourceIds'];
      setFilter(newFilter);
      
    },
    onChangeData: (key, item, isAll) => {
      if (key === 'select-all') {
        if (selectPageGroup.size === orderSourceType.length) {
          setSelectPageGroup(new Map());

          const newFilter = {
            ...filter,
          };
          delete newFilter['sourceTypes'];
          delete newFilter['sourceIds'];
          setFilter(newFilter);
        } else {
          const currMap = new Map();
          orderSourceType.forEach(group => {
            const listPages = sourceAll
              .filter(page => page.type === group.id)
              .map(page => page.id);
            currMap.set(String(group.id), listPages);
          });
          setSelectPageGroup(currMap);


          const sourceTypes = Array.from(currMap.keys());
          const sourceIds = Array.from(currMap.values()).flat();

          const newFilter = {
            ...filter,
            sourceTypes: sourceTypes,
            sourceIds: sourceIds,
          };
          setFilter(newFilter);
        }
        return;
      }

      const currMap = new Map(selectPageGroup);

      if (item?.key === -99) {
        const groupId = key;
        const allPagesInGroup = sourceAll
          .filter(page => page.type === groupId)
          .map(page => page.id);

        if (isAll) {
          currMap.delete(groupId);
        } else {
          currMap.set(groupId, allPagesInGroup);
        }

        setSelectPageGroup(currMap);

        if (currMap.size === 0) {
          const newFilter = {
            ...filter,
          };

          delete (newFilter as any)['sourceTypes'];
          delete (newFilter as any)['sourceIds'];

          setFilter(filter)
        } else {
          updateFilterWithSelected(currMap);
        }
        return;
      }

      if (item) {
        const sourceType = key;
        const pageId = item.key;
        const currentPages = currMap.get(sourceType) || [];

        if (currentPages.includes(pageId)) {
          const updatedPages = currentPages.filter(id => id !== pageId);
          if (updatedPages.length > 0) {
            currMap.set(sourceType, updatedPages);
          } else {
            currMap.delete(sourceType);
          }
        } else {
          currMap.set(sourceType, [...currentPages, pageId]);
        }
        setSelectPageGroup(currMap);

        if (currMap.size === 0) {
          const newFilter = {
            ...filter,
          };

          delete (newFilter as any)['sourceTypes'];
          delete (newFilter as any)['sourceIds'];
          setFilter(newFilter);
        } else {
          updateFilterWithSelected(currMap);
        }
      }
    },
    className: 'multi_select_custom',
    dropdownClassName: 'multi_select_custom_dropdown',
    checkedColor: '#07B2A9',
  };

  const columns = [
    {
      title: '',
      width: 450,
      dataIndex: 'order',
      render: (order: any, record: any) => {
        const addressFull = [
          order?.addressText,
          order?.addressWard,
          order?.addressDistrict,
          order?.addressProvince,
        ]
          .filter(x => !isNil(x) && !isEmpty(x))
          .join(', ');

        const phonePrefix = order?.customerPhone
          ?.replace('+', '')
          ?.replace('0', '')
          ?.slice(0, 2);
        const currentTimesRepeatCare = record?.currentCare?.careItems.reduce(
          (accumulator, currentValue) => {
            if (currentValue.note !== 'from system') {
              accumulator = accumulator + 1;
            }
            return accumulator;
          },
          0,
        );
        return (
          <div
            style={{ display: 'flex', alignItems: 'flex-start', width: 430 }}
          >
            <Box textAlign={'center'}>
              {record?.duplicateLeads?.length > 0 &&
                !record?.ignoreDuplicateWarning && (
                  <img
                    src="/icons/order/CreateOrder-icons-Alert-Filled.svg"
                    alt=""
                  />
                )}
              <Tooltip
                title={
                  record?.latestCareItem
                    ? record?.latestCareItem?.reason?.name
                    : 'Chưa có lí do'
                }
              >
                <div>
                  {record?.latestCareItem ? (
                    <img
                      src={`/icons/telesales/reason/${record?.latestCareItem?.reason?.reasonKey}.svg`}
                      alt=""
                    />
                  ) : (
                    <img
                      src={`/icons/telesales/reason/empty_reason.svg`}
                      alt=""
                    />
                  )}
                  {![
                    CareState.awaiting_stock,
                    CareState.confirmed,
                    CareState.reconfirm,
                    CareState.lost,
                    CareState.junk,
                    CareState.failed,
                  ].includes(record.state) &&
                    record?.latestCareItem?.timesRepeatReason && (
                      <div className="box_square">
                        {record?.latestCareItem?.timesRepeatReason}
                      </div>
                    )}
                  {!!record?.currentCare?.careItems?.length && (
                    <div className="current_care">
                      <p className="text">Care</p>
                      <div className="count">
                        {String(currentTimesRepeatCare).padStart(2, '0')}
                      </div>
                    </div>
                  )}
                </div>
              </Tooltip>
            </Box>

            <div style={{ marginLeft: 24, flex: 1 }}>
              <div className="text_bold">{order?.customerName}</div>
              <HStack
                alignItems={'center'}
                style={{ margin: '4px 0px' }}
                justifyContent={'space-between'}
              >
                <HStack alignItems={'center'}>
                  {order?.customerPhone &&
                    ![
                      CareState.new,
                      CareState.unassign_attempted,
                      CareState.lost,
                      CareState.junk,
                      CareState.assigned,
                    ].includes(record.state) && (
                      <>
                        {YCallStatus ? (
                          <CallButtonWithYCall
                            data={record}
                            devices={devices}
                            placement={'bottomLeft'}
                            makeCall={(deviceId, simSlot) =>
                              __makeCall(deviceId, simSlot, record)
                            }
                          />
                        ) : (
                          <CallButton
                            devices={devices}
                            placement={'bottomLeft'}
                            makeCall={(deviceId, simSlot) =>
                              __makeCall(deviceId, simSlot, record)
                            }
                          />
                        )}

                        {/* <CallButtonWithYCall
                            data={record}
                            devices={devices}
                            placement={'bottomLeft'}
                            makeCall={(deviceId, simSlot) =>
                              __makeCall(deviceId, simSlot, record)
                            }
                          /> */}
                      </>
                    )}
                  <AgSalerCensorComponent
                    userCareId={
                      order?.teamInCharge === 2
                        ? order?.carePageId
                        : order?.saleId
                    }
                    autoCensor={true}
                    saveId={order?.id}
                    defaultCensored={!!order?.id}
                    text={order?.customerPhone}
                    length={4}
                    type={
                      record.state === CareState.assigned
                        ? 'address'
                        : 'copyPhone'
                    }
                    censorView={
                      <div className="text_bold">
                        <img
                          className="ic_lead"
                          src="/icons/telesales/phone.svg"
                          alt=""
                        />
                        {maskTextPhone(order?.customerPhone)}
                      </div>
                    }
                    unCensorView={
                      <div className="text_bold">
                        <img
                          className="ic_lead"
                          src="/icons/telesales/phone.svg"
                          alt=""
                        />
                        {record.state === CareState.assigned &&
                        order?.customerPhone
                          ? maskTextPhone(order?.customerPhone)
                          : order?.customerPhone || ''}
                      </div>
                    }
                    tabActive={tabActive}
                    segment={segmented}
                  />

                  {PhoneNumberPrefix?.[`${phonePrefix}`] &&
                    localStorage.getItem('countryActive') === '84' && (
                      <img
                        src={`/icons/phones/${
                          PhoneNumberPrefix?.[`${phonePrefix}`]
                        }.svg`}
                      />
                    )}
                </HStack>
              </HStack>

              <Box
                display={'flex'}
                alignItems={'flex-start'}
                className="address"
              >
                <img
                  className="ic_lead"
                  src="/icons/telesales/location.svg"
                  alt=""
                />
                <AgSalerCensorComponent
                  userCareId={
                    order?.teamInCharge === 2
                      ? order?.carePageId
                      : order?.saleId
                  }
                  autoCensor={true}
                  saveId={order?.id}
                  defaultCensored={!!order?.id}
                  text={order?.customerPhone}
                  length={4}
                  type={'address'}
                  censorView={<>{maskText(addressFull, 10, 44)}</>}
                  unCensorView={
                    <>
                      {record.state === CareState.assigned
                        ? maskText(addressFull, 10, 44)
                        : addressFull}
                    </>
                  }
                />
              </Box>
              <Space>
                {intersection(filter.state, [CareState.assigned]).length > 0 &&
                  record.userId === UserLogin.id && (
                    <Box
                      color={'#00359E'}
                      backgroundColor={'#E0E8FF'}
                      padding={'4px 12px'}
                      borderRadius={8}
                      fontWeight={500}
                      marginTop={3}
                      cursor={'pointer'}
                      onClick={event => {
                        event.stopPropagation();
                        if (!hasTakeDataPerm) return;
                        _takeAssignedLead(record.id);
                      }}
                    >
                      {transTelesales('common.receive')}
                    </Box>
                  )}
                <div className="time">
                  <img
                    className="ic_lead"
                    src="/icons/telesales/time.svg"
                    alt=""
                  />{' '}
                  {moment(record?.currentCare?.createdAt).fromNow()}
                </div>
                {order?.tags?.map((tag: TagType, idx: number) => {
                  if (idx < 1) {
                    return (
                      <Tag color="magenta" key={idx}>
                        {tag.name}
                      </Tag>
                    );
                  }
                })}

                {Number(order?.tags?.length) - 1 > 0 && (
                  <Tooltip
                    title={
                      <div>
                        {order?.tags?.map((it: any, index: number) => {
                          if (index > 0)
                            return <Box marginBottom={5}>{it?.name}</Box>;
                        })}
                      </div>
                    }
                  >
                    <div
                      style={{
                        color: '#000',
                        backgroundColor: '#EBEDF4',
                        padding: '0px 5px',
                        borderRadius: 3,
                      }}
                    >
                      +{order?.tags?.length - 1}
                    </div>
                  </Tooltip>
                )}
              </Space>
            </div>
          </div>
        );
      },
    },
    {
      title: '',
      width: 300,
      dataIndex: 'order',
      render: (order: any, record: any, index: any) => {
        const upComingAppointments = orderBy(
          record?.appointments.filter(it => it.appointmentTime > new Date()),
          'appointmentTime',
          'asc',
        );

        return (
          <div>
            <div className="text_price">
              <img
                className="ic_lead"
                src="/icons/telesales/market.svg"
                alt=""
              />
              {PriceUtils.format(
                order?.totalPrice +
                  Number(order.shippingFee) +
                  Number(order.surcharge) -
                  Number(order?.discount),
              )}
            </div>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Tag
                style={{
                  marginBottom: 8,
                  marginTop: 10,
                  background: '#EBEDF4',
                  color: '#000',
                  fontWeight: 500,
                  border: 'none',
                  padding: '2px 6px',
                }}
                color="default"
              >
                {record.state === CareState.assigned
                  ? `${order?.displayId?.slice(0, 4)}${new Array(3)
                      .fill('*')
                      .join('')}`
                  : order?.displayId}
              </Tag>
            </div>
            <div>
              {order?.products?.length > 0 && (
                <RenderProduct data={order?.products} />
              )}
            </div>
            {record?.appointments?.length > 0 && (
              <Flex marginTop={6} alignItems={'center'}>
                <Badge count={record?.appointments?.length}>
                  <img src="/icons/telesales/schedule.svg" />
                </Badge>
                {upComingAppointments?.length > 0 && (
                  <div
                    style={{ color: '#000', fontWeight: '500', marginLeft: 20 }}
                  >
                    {upComingAppointments?.[0]?.content}
                  </div>
                )}
              </Flex>
            )}
          </div>
        );
      },
    },
    {
      title: '',
      render: record => {
        // const careItems = orderBy(record?.cares, "id", "desc");
        const cares = uniqBy(record?.cares, 'userId');

        return (
          <div>
            <Flex width={500} marginBottom={10}>
              <div style={{ display: 'flex' }}>
                <div style={{ marginRight: 40 }}>
                  <div className="care">{transTelesales('common.source')}</div>
                  <div className="care_value" style={{ display: 'flex' }}>
                    {record?.order?.externalSource?.entity ===
                    SourceEntity.fb_page ? (
                      <Tooltip
                        title={`https://fb.com/${record?.order?.externalSource?.entityId}`}
                      >
                        <Flex
                          alignItems={'center'}
                          justifyContent={'center'}
                          width={28}
                          height={28}
                          borderRadius={4}
                          background={'#EBEDF4'}
                        >
                          <img
                            style={{ width: 20, height: 20 }}
                            src="/icons/common/fb_black.svg"
                            alt=""
                          />
                        </Flex>
                      </Tooltip>
                    ) : (
                      <Tooltip
                        title={TextUtils.findClient(
                          landings,
                          record?.order?.externalSource?.entityId,
                        )}
                      >
                        <Flex
                          alignItems={'center'}
                          justifyContent={'center'}
                          width={28}
                          height={28}
                          borderRadius={4}
                          background={'#EBEDF4'}
                        >
                          <img
                            style={{ width: 20, height: 20 }}
                            src="/icons/common/global_black.svg"
                            alt=""
                          />
                        </Flex>
                      </Tooltip>
                    )}
                    {record?.order?.fbScopedUserId ? (
                      <Tooltip title={'Facebook Conversion'}>
                        <Flex
                          alignItems={'center'}
                          justifyContent={'center'}
                          width={28}
                          height={28}
                          borderRadius={4}
                          background={'#EBEDF4'}
                          marginLeft={5}
                        >
                          <IconCommentLine
                            width={20}
                            height={20}
                            fill="black"
                          />
                        </Flex>
                      </Tooltip>
                    ) : record?.formCapturedAt ? (
                      <Tooltip title={'Capture Form'}>
                        <Flex
                          alignItems={'center'}
                          justifyContent={'center'}
                          width={28}
                          height={28}
                          borderRadius={4}
                          background={'#EBEDF4'}
                          marginLeft={5}
                        >
                          <img
                            style={{ width: 20, height: 20 }}
                            src="/icons/common/capture-form.svg"
                            alt=""
                          />
                        </Flex>
                      </Tooltip>
                    ) : (
                      <Tooltip title={'Manual Keying'}>
                        <Flex
                          alignItems={'center'}
                          justifyContent={'center'}
                          width={28}
                          height={28}
                          borderRadius={4}
                          background={'#EBEDF4'}
                          marginLeft={5}
                        >
                          <img
                            style={{ width: 20, height: 20 }}
                            src="/icons/common/icon_Manual key.svg"
                            alt=""
                          />
                        </Flex>
                      </Tooltip>
                    )}
                  </div>
                </div>
              </div>
              <div style={{ display: 'flex' }}>
                <div style={{ marginRight: 20 }}>
                  <div className="care">
                    {transTelesales('common.care_staff')}
                  </div>
                  <div className="care_value" style={{ display: 'flex' }}>
                    <span>{userReduce[record?.currentCare?.userId]?.name}</span>
                    {cares?.length - 1 > 0 && (
                      <Tooltip
                        title={
                          <div>
                            {cares?.map((care: any) => {
                              if (record?.currentCare?.userId !== care?.userId)
                                return (
                                  <Box marginBottom={5}>
                                    {userReduce[care?.userId]?.name}
                                  </Box>
                                );
                            })}
                          </div>
                        }
                      >
                        <div
                          style={{
                            color: '#000',
                            backgroundColor: '#EBEDF4',
                            padding: '0px 5px',
                            borderRadius: 3,
                            width: 'fit-content',
                            marginLeft: 5,
                          }}
                        >
                          +{cares?.length - 1}
                        </div>
                      </Tooltip>
                    )}
                  </div>
                </div>
              </div>
            </Flex>

            {record?.latestCareItem && (
              <Flex
                color={'#667085'}
                fontSize={14}
                lineHeight={'20px'}
                border={'1px solid #D0D5DD'}
                borderRadius={4}
                padding={'4px 8px'}
                width={'fit-content'}
                marginTop={5}
              >
                <img
                  src="/images/support.svg"
                  style={{ marginRight: 8, width: 16 }}
                />
                {`${moment(record?.latestCareItem?.createdAt).format(
                  'DD-MM-YY HH:mm A',
                )} - “ ${transTelesales(
                  record?.latestCareItem?.reason?.name,
                )}”`}
              </Flex>
            )}
          </div>
        );
      },
    },
  ];

  const dataRender = useMemo(
    () => data.filter(item => item.state === segmented || segmented === 'all'),
    [data],
  );

  const _onChangeUser = (values: any[]) => {
    if (
      values?.length === 1 &&
      values?.[0]?.[0] === 'custom' &&
      values?.[0]?.length === 1
    ) {
      // chọn tất cả trừ tôi
      setFilter({
        ...filter,
        excludeUserIds: [UserLogin.id],
        userIds: undefined,
        usedToBeAssigned: undefined,
      });
    } else if (
      values?.length === 1 &&
      values?.[0]?.[0] === 'usedToBeAssigned' &&
      values?.[0]?.length === 1
    ) {
      setFilter({
        ...filter,
        excludeUserIds: undefined,
        userIds: undefined,
        usedToBeAssigned: true,
      });
    } else if (
      values?.length === 1 &&
      values?.[0]?.[0] === 'onlyMe' &&
      values?.[0]?.length === 1
    ) {
      setFilter({
        ...filter,
        userIds: [UserLogin.id],
        excludeUserIds: undefined,
        usedToBeAssigned: undefined,
      });
    } else {
      if (values?.length == 2 && values?.[1]?.length === 1) {
        // chọn tất cả
        setFilter({
          ...filter,
          excludeUserIds: undefined,
          userIds: [],
          usedToBeAssigned:
            values?.[1]?.[0] === 'usedToBeAssigned' ? true : undefined,
        });
      } else {
        let userIds: any = [];
        userIds = reduce(
          values,
          (item: any, next: any) => {
            if (next?.[0] === 'onlyMe') {
              item.push(UserLogin.id);
            } else {
              item.push(next?.[1]);
            }
            return item;
          },
          [],
        );

        setFilter({
          ...filter,
          userIds,
          usedToBeAssigned: undefined,
          excludeUserIds: undefined,
        });
      }
    }
  };

  const _closeDetailLead = () => {
    setVisibleLeadDetail(false);
    setLeadDetail(undefined);
  };

  const _onChangeTab = tab => {
    router.push({
      query: { tab: tab?.key, seg: 'all' },
    });
    setTabActive(tab.key);
    setFilter({ ...filter, state: tab.state, page: 1 });
    setSegmented('all');
    _refresh();
  };

  const _onChangeSeg = (state, value) => {
    router.push({
      query: { seg: value, tab: tabActive },
    });
    setFilter({ ...filter, state, page: 1 });
    _refresh();
  };
  const _refresh = () => {
    loadMoreRef.current = false;
    loadDoneRef.current = false;
    currentPageRef.current = 1;
    const tableBody = document.querySelector('.ant-table-body');
    tableBody.scrollTop = 0;
  };

  const _fetchMoreData = async () => {
    message.loading({ content: transTelesales('fetching'), key: 'fetching' });
    setFetching(true);
    const { response, error } = await OrderRepository.takeCareLeads();
    setFetching(false);
    if (response?.data) {
      const { newEligible, revokedEligible, newLeads, revokedLeads, noReq } =
        response.data;
      message.success({
        content: (
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'flex-start',
            }}
          >
            <div
              style={{ marginBottom: 15, textAlign: 'left', paddingLeft: 12 }}
            >{`Lần lấy dữ liệu thứ ${noReq} trong ngày`}</div>
            <div
              style={{ marginBottom: 15, textAlign: 'left', paddingLeft: 12 }}
            >{`Dữ liệu tính toán: ${newEligible} data mới, ${revokedEligible} data đảo`}</div>
            <div
              style={{ paddingLeft: 12, textAlign: 'left' }}
            >{`Dữ liệu thực tế đã nhận: ${newLeads} mới, ${revokedLeads} data đảo`}</div>
          </div>
        ),
        key: 'fetching',
        duration: 5,
        className: 'custom_message',
        icon: (
          <img
            src="/icons/telesales/checkbox-circle-line.svg"
            style={{ width: 20, marginRight: 5 }}
          />
        ),
      });
      _fetchLeads();
      _fetchDataCount();
    } else {
      message.error({
        content: error?.response?.data?.message,
        key: 'fetching',
        duration: 2,
      });
    }
  };

  const _getSources = sources => {
    return sources.filter(
      it => it.countryId === Number(localStorage.getItem('countryActive')),
    );
  };

  // const _onChangeSource = (values: any [])=>{
  //   if(values?.)
  // }

  const stateActives = GroupStateManager.filter(it => it.key === tabActive)?.[0]
    ?.state;

  const checkAdvancedFilter = () => {
    if (!isNil(filter.sourceType) && filter?.sourceIds?.length > 0) return true;
    return false;
  };

  const __makeCall = async (
    deviceId: string,
    simSlot: number,
    leadCall: Lead,
  ) => {
    const phone =
      localStorage.getItem('phoneTest') || leadCall?.order?.customerPhone;
    const { response, error } = await UserRepository.makeCall(deviceId, {
      phoneNumber: phone,
      leadId: leadCall?.id,
      simSlot,
    });
    if (response?.data) {
      _startCall(deviceId, phone);
    } else {
      message.error(error?.response?.data?.message);
    }
  };

  const endCallMessage = (phoneNumber: string) => {
    message.success({
      content: (
        <HStack
          width={400}
          alignItems={'center'}
          justifyContent={'space-between'}
          position={'relative'}
        >
          <Box
            width={30}
            top={-16}
            left={-22}
            right={0}
            bottom={-16}
            zIndex={-1}
            position={'absolute'}
            borderRadius={22}
            background={'#BD2026'}
          />
          <HStack gap={'10px'}>
            <img src="/icons/order/endCall.svg" alt="" />
            <Box
              display={'flex'}
              flexDirection={'column'}
              alignItems={'flex-start'}
            >
              <Box fontSize={16} fontWeight={600} lineHeight={'28px'}>
                {phoneNumber}
              </Box>
              <HStack>
                <Box fontSize={14} color={'#667085'} fontWeight={400}>
                  {tOrder('Ended')}
                </Box>
              </HStack>
            </Box>
          </HStack>
          <Box onClick={() => message.destroy('calling')}>
            <img src="/icons/order/close.svg" alt="" />
          </Box>
        </HStack>
      ),
      key: 'calling',
      duration: 3,
      icon: false,
      className: '_myCall_Message',
    });
  };

  const openMessage = (phoneNumber: string) => {
    message.loading({
      content: (
        <HStack
          width={400}
          alignItems={'center'}
          justifyContent={'space-between'}
          position={'relative'}
        >
          <Box
            width={30}
            top={-16}
            left={-22}
            right={0}
            bottom={-16}
            zIndex={-1}
            position={'absolute'}
            borderRadius={22}
            background={'#00359E'}
          />
          <HStack gap={'10px'}>
            <img src="/icons/order/calling.svg" alt="" />
            <Box
              display={'flex'}
              flexDirection={'column'}
              alignItems={'flex-start'}
            >
              <Box fontSize={16} fontWeight={600} lineHeight={'28px'}>
                {phoneNumber}
              </Box>

              <Box display={'flex'} alignItems={'center'}>
                <Box fontSize={14} color={'#667085'} fontWeight={400}>
                  {tOrder('Calling')}
                </Box>
                <Box
                  marginInlineStart={'1.5rem'}
                  display={'flex'}
                  alignItems={'center'}
                  marginLeft={20}
                  className="snippet"
                  data-title="dot-flashing"
                >
                  <Box className="stage">
                    <div className="dot-flashing"></div>
                  </Box>
                </Box>
              </Box>
            </Box>
          </HStack>
          <Box onClick={() => message.destroy('calling')}>
            <img src="/icons/order/close.svg" alt="" />
          </Box>
        </HStack>
      ),
      key: 'calling',
      duration: 1000000,
      icon: false,
      className: '_myCall_Message',
    });
  };

  const _startCall = async (deviceId: string, phoneNumber: string) => {
    callStateRef.current = UserDeviceCallState.CALL_STATE_OFFHOOK;
    openMessage(phoneNumber);

    await UserRepository.pingDevice(deviceId, {
      state: 2,
    });

    while (callStateRef.current === UserDeviceCallState.CALL_STATE_OFFHOOK) {
      const { response } = await UserRepository.getDevice(deviceId);

      if (response?.data) {
        callStateRef.current = response?.data?.status;
        if (response?.data?.status === UserDeviceCallState.CALL_STATE_IDLE) {
          endCallMessage(phoneNumber);
          break;
        }
      } else {
        callStateRef.current = UserDeviceCallState.CALL_STATE_IDLE;
        endCallMessage(phoneNumber);
        break;
      }
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  };

  const haveAdvancedFilter = checkAdvancedFilter();
  const hasFilterPerm = useHasPermission(SalePermission.telesales, [
    TelesalesPermission.assignedLeadsFilter,
  ]);
  const hasTakeDataPerm = useHasPermission(SalePermission.telesales, [
    TelesalesPermission.takeCareLeads,
  ]);

  const hasAppointmentPerm = useHasPermission(SalePermission.telesales, [
    TelesalesPermission.appointments,
  ]);
  const hasFetchOnePerm = useHasPermission(SalePermission.telesales, [
    TelesalesPermission.fetchOne,
  ]);
  const handleClickSortByTime = () => {
    if (filter.sort === 'DESC') {
      setFilter({ ...filter, sort: 'ASC', page: 1 });
    } else {
      setFilter({ ...filter, sort: 'DESC', page: 1 });
    }
    _refresh();
  };
  const handleShowOptionSort = () => {
    setOpenSortPopup(!openSortPopup);
  };
  const onSelectSort = value => {
    setFilter({
      ...filter,
      orderBy: value,
      page: 1,
    });
    _refresh();
    setOpenSortPopup(false);
  };
  const getContentSort = () => (
    <div
      className="sort-content"
      onMouseDown={e => {
        e.stopPropagation();
      }}
    >
      <div
        className={`sort-item ${
          filter?.orderBy === 'createdAt' && 'sort-item-active'
        }`}
        key="createdAt"
        onClick={() => onSelectSort('createdAt')}
      >
        {transTelesales('action.sort_by_creation_time')}
        {filter?.orderBy === 'createdAt' && (
          <img src="/icons/dashboard/checked.svg" alt="" />
        )}
      </div>
      <div
        className={`sort-item ${
          filter?.orderBy === 'reasonUpdateTime' && 'sort-item-active'
        }`}
        key="reasonUpdateTime"
        onClick={() => onSelectSort('reasonUpdateTime')}
      >
        {transTelesales('action.sort_by_reason_update_time')}
        {filter?.orderBy === 'reasonUpdateTime' && (
          <img src="/icons/dashboard/checked.svg" alt="" />
        )}
      </div>
    </div>
  );

  return (
    <LeadContextProvider
      value={{
        leadDetail,
        setLeadDetail,
        visibleLeadDetail,
        setVisibleLeadDetail,
      }}
    >
      <Container>
        <div className="content">
          <div className="header">
            <div className="employee_filter_header">
              {hasFilterPerm && (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Input
                    onReset={() => setFilter({ ...filter, query: '' })}
                    onChange={e => setQuery(e.target.value)}
                    value={query}
                    onKeyPress={_onKeyPress}
                    style={{ flex: 1, maxWidth: 220 }}
                    placeholder={transTelesales('common.search_placeholder')}
                    className={`input_custom_ lead_manager_search_ip`}
                  />
                  <AGSaleRangePicker
                    allowClear={true}
                    beginAt={Number(filter?.fromCurrentCare)}
                    endAt={Number(filter?.toCurrentCare)}
                    onRemove={() =>
                      setFilter({
                        ...filter,
                        fromCurrentCare: undefined,
                        toCurrentCare: undefined,
                      })
                    }
                    onChange={_onChangeDateHandOver}
                    showRanges={true}
                    closeDateRange={true}
                  />
                  <Cascader
                    multiple
                    showSearch
                    style={{ width: 250, marginRight: 10, marginLeft: 10 }}
                    maxTagCount="responsive"
                    placeholder="Điều kiện dữ liệu"
                    dropdownClassName="my_cascader"
                    options={
                      isTelesalesManager
                        ? [
                            {
                              value: 'onlyMe',
                              label: transTelesales('common.only_me'),
                            },
                            {
                              value: 'custom',
                              label: transTelesales('common.exclude_me'),
                              children: telesales?.map(item => {
                                return { value: item.id, label: item.name };
                              }),
                            },
                          ]
                        : [
                            {
                              value: 'onlyMe',
                              label: transTelesales('common.only_me'),
                            },
                            {
                              value: 'usedToBeAssigned',
                              label: transTelesales('common.used_to_take_care'),
                            },
                          ]
                    }
                    onChange={value => _onChangeUser(value)}
                    
                  />
                  <AGMultiSelectWithTab
                    textType={transTelesales('history.reason')}
                    data={orderBy(
                      careReasons.filter(
                        r => r.reasonKey !== 'origin_order_status_reverted',
                      ),
                      'name',
                      'asc',
                    )}
                    value={filter?.reasonIds}
                    popOverStyle={{ width: 250 }}
                    onChange={(ids: any[]) =>
                      setFilter({
                        ...filter,
                        reasonIds: ids,
                      })
                    }
                    containerStyle={{
                      marginRight: 10,
                      maxHeight: 40,
                      backgroundColor: '#E3E5EE',
                      borderColor: '#E3E5EE',
                    }}
                    onChangeTab={tab =>
                      setFilter({
                        ...filter,
                        reasonTab: tab,
                      })
                    }
                  />
                  <AGMultiSelect
                    textType={transTelesales('product')}
                    // placeholder="Select Projects."
                    data={orderBy(products, 'name', 'asc')}
                    value={filter?.productIds}
                    popOverStyle={{ width: 250 }}
                    onChange={(ids: any[]) =>
                      setFilter({
                        ...filter,
                        productIds: ids,
                      })
                    }
                    containerStyle={{
                      maxHeight: 40,
                      backgroundColor: '#E3E5EE',
                      borderColor: '#E3E5EE',
                    }}
                  />
                  <SelectMultiComponentSale
                    data={mockData.data}
                    title={mockData.title}
                    clear={mockData.clear}
                    orderText={transTelesales}
                    clearFilter={mockData.clearFilter}
                    onChangeData={mockData.onChangeData}
                    className={mockData.className}
                    dropdownClassName={mockData.dropdownClassName}
                    checkedColor={mockData.checkedColor}
                    hasSelect={hasSelect}
                    clearIcon='/icons/icons/Close/Line.svg'
                  />
                </div>
              )}
              <div style={{ display: 'flex', alignItems: 'center' }}>
                {hasTakeDataPerm && (
                  <ActionButton
                    style={{ margin: '0px 10px' }}
                    text={transTelesales('Get Data')}
                    onClick={_fetchMoreData}
                    type="BlackBorder"
                    loading={_fetching}
                    icon="/icons/ffm/order/export.svg"
                  />
                )}
                {/* {hasFilterPerm && (
                  <div className="advanced_filter_wrap">
                    {haveAdvancedFilter && <div className="dot_active" />}
                    <ActionButton
                      style={{ margin: '0px 10px 0px 0px' }}
                      type="BlackBorder"
                      icon="/icons/ffm/order/filter.svg"
                      text={tOrder('filter.more_filter')}
                      onClick={() => setVisibleAdvancedFilter(true)}
                    />
                  </div>
                )} */}

                {schedules.length > 0 && hasAppointmentPerm && (
                  <Dropdown
                    overlay={<AppointmentSchedule schedules={schedules} />}
                    placement="bottomRight"
                    trigger={['click']}
                  >
                    <Flex
                      alignItems={'center'}
                      color={'#00359E'}
                      border={'1px solid #00359E'}
                      borderRadius={8}
                      height={40}
                      marginLeft={10}
                      padding={'0px 10px'}
                      cursor={'pointer'}
                    >
                      <SvgRender
                        src="/icons/Calendar/Line"
                        color={'#00359E'}
                        id="Calendar"
                      />
                      <Box
                        color={'#00359E'}
                        fontSize={14}
                        lineHeight={'20px'}
                        fontWeight={500}
                        marginLeft={10}
                        marginRight={10}
                      >
                        {transTelesales('common.appointment_schedule')}
                      </Box>
                      <Badge
                        count={
                          schedules.filter(
                            it => it.appointmentTime > moment().valueOf(),
                          )?.length
                        }
                        style={{
                          backgroundColor: '#00359E',
                          color: '#fff',
                        }}
                      />
                    </Flex>
                  </Dropdown>
                )}
              </div>
            </div>
          </div>
          <LeadTab
            filter={filter}
            dataCount={dataCount}
            onChangeTabActive={_onChangeTab}
            tabActive={tabActive}
          />
          <div className="wrap_segment">
            {!filter.state.includes(CareState.assigned) && (
              <Segmented
                onChange={(segmented: CareState) => setSegmented(segmented)}
                options={[
                  {
                    label: (
                      <div
                        // onClick={()=>{
                        //   setFilter({ ...filter, state: stateActives , page: 1 });

                        // }}
                        onClick={() => _onChangeSeg(stateActives, 'all')}
                      >
                        {transTelesales('common.all')}(
                        {sumBy(
                          dataCount.filter(it =>
                            stateActives.includes(it.state),
                          ),
                          it => Number(it.count),
                        )}
                        )
                      </div>
                    ),
                    value: 'all',
                  },
                  ...stateActives?.map((state: CareState) => ({
                    label: (
                      <div onClick={() => _onChangeSeg([state], state)}>
                        {TextUtils.findClient(
                          CareStateConfig,
                          state,
                          'id',
                          i18n.language === 'vi' ? 'text' : 'text_en',
                        )}{' '}
                        (
                        {sumBy(
                          dataCount.filter(it => it.state === state),
                          it => Number(it.count),
                        )}
                        )
                      </div>
                    ),
                    value: state,
                  })),
                ]}
                value={segmented}
              />
            )}
            <Popover
              trigger={'manual'}
              placement="bottomRight"
              content={getContentSort}
              overlayClassName="select_sort_pop_over_wrap"
              open={openSortPopup}
              onOpenChange={visible => setOpenSortPopup(visible)}
            >
              <div className="sort-btn">
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'center',
                    cursor: 'pointer',
                  }}
                  onClick={handleShowOptionSort}
                  ref={ref}
                >
                  <TimeIcon />
                </div>

                <div
                  className={classNames('arrow-icon', {
                    rotate: filter.sort === 'ASC',
                  })}
                  onClick={handleClickSortByTime}
                >
                  <UpIcon />
                </div>
              </div>
            </Popover>
          </div>
          <div className="wrap_table">
            <Table
              loading={loading}
              scroll={{ x: 174 * 8, y: `calc(100vh - 330px)` }}
              dataSource={dataRender}
              columns={columns}
              id="lead_table_manager"
              locale={{
                emptyText: transTelesales('common.empty_data'),
              }}
              rowClassName={(record: any) => {
                return `_order_table_row ${
                  record?.duplicateLeads?.length > 0 &&
                  !record?.ignoreDuplicateWarning
                    ? '_duplicate_order'
                    : ''
                }`;
              }}
              pagination={false}
              onRow={(record: any) => {
                return {
                  onClick: e => {
                    if (!hasFetchOnePerm) return;
                    if ([CareState.assigned].includes(record.state)) return;
                    if (e.ctrlKey || e.metaKey) {
                      window.open(
                        `/telesales/leads-management/${record.id}`,
                        '_blank',
                      );
                    } else {
                      // setVisibleLeadDetail(true);
                      setLeadDetail(record);
                      router.push(`/telesales/leads-management/${record.id}`);
                    }
                  },
                };
              }}
            />
          </div>
          <Flex
            justifyContent={'flex-end'}
            alignItems={'center'}
            marginTop={10}
            padding={'0px 10px'}
          >
            <Pagination
              onChange={paginationChange}
              pageSize={internalFilter.limit}
              current={internalFilter?.page}
              total={sumBy(
                dataCount.filter(it => filter.state.includes(it.state)),
                it => Number(it.count),
              )}
              size="small"
              showSizeChanger={true}
              pageSizeOptions={[100, 200, 300, 500, 1000]}
              locale={{
                page: 'trang',
              }}
              showQuickJumper={true}
            />
          </Flex>
        </div>
        {/* {visibleAdvancedFilter && (
          <Drawer
            placement="left"
            title={tMess('filter.advanced')}
            closable={false}
            width={500}
            onClose={() => setVisibleAdvancedFilter(false)}
            visible={visibleAdvancedFilter}
          >
            <LeadAdvancedFilter
              filter={filter}
              onChangeFilter={filter => {
                setFilter(filter);
                setVisibleAdvancedFilter(false);
              }}
              onClose={() => setVisibleAdvancedFilter(false)}
            />
          </Drawer>
        )} */}
      </Container>
      {visibleLeadDetail && (
        <WrapLeadDetail>
          <div style={{ marginBottom: 14 }}>
            <HeaderPosition
              title={tMenu('telesales.lead_detail')}
              onBack={_closeDetailLead}
            />
          </div>
          <div style={{ marginLeft: 13 }}>
            <LeadDetail
              id={leadDetail.id}
              onChangeSuccess={_onChangeDataDetailSuccess}
            />
          </div>
        </WrapLeadDetail>
      )}
    </LeadContextProvider>
  );
};

const WrapLeadDetail = styled.div`
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  z-index: 10;
  background: #f0f2f5;
  left: 0px;
`;

const Container = styled.div`
  background: #ffffff;
  border-radius: 4px 0px 0px 4px;

  ._duplicate_order {
    background: #ffeac0 !important;

    .ant-table-cell-fix-left {
      background: #ffeac0 !important;
    }

    &:hover {
      td {
        background: #ffeac0 !important;
      }
    }
  }
  .lead_manager_search_ip {
    /* width: 400px; */
  }

  .advanced_filter_wrap {
    position: relative;

    .advanced_filter {
      cursor: pointer;
      /* filter: grayscale(var(--value, 100%)); --value:100%; */
    }

    .dot_active {
      width: 10px;
      height: 10px;
      border-radius: 10px;
      background: #bd2026;
      position: absolute;
      top: 8px;
      left: 20px;
    }
  }

  .box_square {
    margin-top: 10px;
    background: #ebedf4;
    width: 24px;
    height: 24px;
    border-radius: 7px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    color: #000000;
  }

  .current_care {
    margin-top: 14px;
    .text {
      font-size: 8px;
      line-height: 14px;
      font-weight: 600;
      margin-bottom: 0;
    }
    .count {
      font-size: 14px;
      line-height: 20px;
      font-weight: 500;
    }
  }

  .content {
    border-width: 1px 1px 1px 1px;
    border-style: solid;
    border-color: #d0d5dd;
    border-radius: 6px;
    background-color: #fff;
    padding-bottom: 10px;

    .header {
      padding: 11px 14px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .big_title {
        color: #667085;
        font-family: 'Be Vietnam Pro';
        font-style: normal;
        font-weight: 500;
        font-size: 16px;
        line-height: 20px;
      }
    }

    .status {
      width: 8px;
      height: 8px;
      border-radius: 4px;
      margin-right: 5px;
    }

    .name {
      font-family: 'Be Vietnam Pro';
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
      color: #000;
    }

    .description {
      font-family: 'Be Vietnam Pro';
      font-style: normal;
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      /* identical to box height, or 133% */

      display: flex;
      align-items: center;
      margin-top: 10px;
      color: #667085;
    }
  }

  .ic_lead {
    margin-right: 5px;
  }

  .wrap_table {
    padding: 2px;
    margin: 0px 10px;
    border: 1px solid #d0d5dd;
    border-radius: 8px;
    padding-bottom: 10px;
  }

  .care {
    font-family: 'Be Vietnam Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    /* identical to box height, or 133% */

    /* Content/Secondary */

    color: #667085;
  }

  .care_value {
    font-family: 'Be Vietnam Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    margin-top: 6px;
    color: #000000;
  }

  .text_price {
    font-family: 'Be Vietnam Pro';
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    color: #667085;
  }

  .variants {
    font-family: 'Be Vietnam Pro';
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    color: #0062ff;
    margin-bottom: 4px;
  }

  .address {
    font-family: 'Be Vietnam Pro';
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    color: #667085;
    margin-top: 4px;
    margin-bottom: 6px;
  }

  .time {
    font-family: 'Be Vietnam Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    color: #667085;
  }

  .text_bold {
    font-family: 'Be Vietnam Pro';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: #000000;
  }

  .input_custom_ {
    border: 1px solid #d0d5dd;
    border-radius: 8px;
    height: 40px !important;
    margin-right: 10px;

    .ant-input-number-input {
      height: 40px !important;
    }
  }

  .select_custom_ {
    margin-right: 10px;
    width: 200px;

    .ant-select-selector {
      border: 1px solid #d0d5dd;
      border-radius: 8px;
      height: 40px;

      .ant-select-selection-placeholder {
        display: flex;
        align-items: center;
      }
    }

    ._select_custom_setting {
      height: 40px;
    }
  }

  .employee_filter_header {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-between;
  }

  .ant-picker-range {
    height: 40px;
    border-radius: 8px;
  }

  .ant-input-affix-wrapper {
    height: 40px;
    border-radius: 8px;
  }

  .ant-segmented {
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #d0d5dd;
    font-style: normal;
    font-weight: 00;
    font-size: 14px;
    line-height: 20px;
    color: #667085;
  }

  .ant-segmented-item-selected {
    background-color: #000;
    border-radius: 8px;
    color: #fff;
  }

  .ant-select-selection-item {
    display: flex;
    align-items: center;
  }

  .wrap_segment {
    padding: 10px 14px;
    padding-top: 0px;
    min-height: 44px;
    position: relative;
    .sort-btn {
      position: absolute;
      right: 14px;
      top: 0;
      background: #e3e5ee;
      border-radius: 8px;
      padding: 10px 12px;
      height: 36px;
      display: flex;
      align-items: center;

      .arrow-icon {
        cursor: pointer;
        margin-left: 8px;
        height: 16px;
        &.rotate {
          transform: rotate(180deg);
        }
      }
    }
  }

  .ant-table-body {
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
      background-color: #f1f1f5;
    }

    ::-webkit-scrollbar-thumb {
      border-radius: 3px;
      background-color: #000000;
      border: 3px solid #c7c7c7;
    }
  }

  .ant-table-header {
    display: none;
  }

  .ant-cascader {
    height: 40px !important;

    .ant-select-selector {
      height: 40px !important;
      border-radius: 6px;
      background-color: #e3e5ee;
      border-color: #e3e5ee !important;
      color: #2c2a2a;
      box-shadow: none !important;

      .ant-select-selection-placeholder {
        color: #2c2a2a;
      }
    }
    .ant-select:not(.ant-select-disabled):hover .ant-select-selector {
      border-color: #e3e5ee !important;
    }
  }

  .ant-table-cell {
    padding: 8px 7px !important;
  }

  .multi_select_custom {
    margin-left: 10px;
    height: 40px;

    .multi_select_custom_dropdown {
      height: 40px;
      background: #E3E5EE;
      border-color: #E3E5EE;
    }
  }
`;
export default LeadManager;
