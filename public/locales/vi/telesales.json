{"table": {"phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "customer": "<PERSON><PERSON><PERSON><PERSON>", "care_staff": "Ngườ<PERSON> phụ trách", "last_care": "Lý do mới nhất", "times": "<PERSON><PERSON>n thứ {{count_times}}", "source": "<PERSON><PERSON><PERSON><PERSON>", "last_care_at": "<PERSON><PERSON><PERSON> s<PERSON> g<PERSON> n<PERSON>t", "updated_at": "<PERSON><PERSON><PERSON> nh<PERSON>t gần nhất", "created_at": "<PERSON><PERSON><PERSON>", "state": "<PERSON><PERSON><PERSON><PERSON> thái <PERSON> liệu", "total_price": "<PERSON><PERSON><PERSON> tiền", "note": "<PERSON><PERSON><PERSON>", "collect_type": "<PERSON><PERSON><PERSON> thu thập dữ liệu", "reasons": "Số lần gắn lý do", "load_more": "<PERSON><PERSON> tải thêm dữ liệu", "refresh_setting": "Đặt về mặc định", "shown_columns": "<PERSON><PERSON><PERSON> hiển thị", "hide_columns": "<PERSON><PERSON><PERSON>", "tag": "Thẻ", "selected": "{{count}} đ<PERSON> ch<PERSON>n", "origin_project": "Dự án gốc", "product": "<PERSON><PERSON><PERSON> p<PERSON>m"}, "action": {"confirm_gather_all_lead": "<PERSON><PERSON><PERSON> nhận thu hồi tất cả dữ liệu không?", "confirm_gather_state_lead": "<PERSON><PERSON><PERSON> nhận thu hồi {{count}} dữ liệu ở trạng thái {{state}} không?", "gather_lead_successfully": "<PERSON>hu hồi dữ liệu thành công", "export_data_successfully": "<PERSON><PERSON>t dữ liệu thành công", "gather_lead": "<PERSON><PERSON> h<PERSON> liệu", "export_excel": "<PERSON><PERSON><PERSON> dữ liệu", "distribution_lead": "<PERSON><PERSON> liệu", "import_excel": "<PERSON><PERSON><PERSON><PERSON> liệu", "get_data": "Thêm Data", "fetching": "<PERSON><PERSON> l<PERSON> thêm <PERSON>", "gather": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "assigned": "<PERSON><PERSON> tiế<PERSON> n<PERSON>n", "no_attempt": "<PERSON><PERSON><PERSON> lý", "attempted": "Đã xử lý", "potential": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>ng", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "create_data": "<PERSON><PERSON><PERSON> dữ liệu", "duplicate": "Tr<PERSON><PERSON> dữ liệu", "sort_by_creation_time": "<PERSON><PERSON><PERSON> xếp theo thời gian tạo", "sort_by_reason_update_time": "<PERSON><PERSON><PERSON> xếp theo thời gian cập nhật lý do", "sort_by_care_frequency": "<PERSON><PERSON><PERSON> xếp theo số lần cập nhật lý do"}, "filter": {"filter_name_is_empty": "<PERSON><PERSON>n ch<PERSON>a nhập tên bộ lọc", "saved_filter_successfully": "<PERSON><PERSON><PERSON> bộ lọc thành công", "an_error": "Đã có lỗi xảy ra", "saved_filter": "<PERSON>ộ lọc đã lưu", "manual_filter": "<PERSON><PERSON> lọc thủ công", "source": "<PERSON><PERSON><PERSON><PERSON>", "select_source": "<PERSON><PERSON><PERSON> ng<PERSON>n", "state_and_reason": "Trạng thái và lý do", "select_state": "<PERSON><PERSON><PERSON> trạng thái", "select_reason": "Chọn lý do", "select_tags": "<PERSON><PERSON><PERSON> thẻ", "times": "<PERSON><PERSON><PERSON>", "numberOfRepeats": "DATA BỊ ĐẢO ≥ X LẦN", "numberOfCares": "SỐ LẦN GẮN LÍ DO", "timeLastCare": "THỜI GIAN CẬP NHẬT", "filter_guide": "Lọc dữ liệu bằng cách chọn một bộ lọc có sẵn trong danh sách", "removed_filter_successfully": "<PERSON><PERSON><PERSON> bộ lọc thành công", "confirm_remove_filter": "<PERSON><PERSON><PERSON> n<PERSON>n x<PERSON><PERSON> bộ lọc", "confirm": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "query": "<PERSON><PERSON><PERSON>", "type_query": "<PERSON><PERSON><PERSON><PERSON> số đi<PERSON> tho<PERSON>, tên kh<PERSON><PERSON> hàng", "collect_type": "<PERSON><PERSON><PERSON> thu thập dữ liệu", "select_collect_type": "<PERSON><PERSON><PERSON> cách thu thập dữ liệu", "include": "<PERSON><PERSON>", "exclude": "Lo<PERSON><PERSON> trừ", "and": "Và", "or": "Hoặc"}, "common": {"receive": "<PERSON><PERSON><PERSON><PERSON>ử lý", "successfully": "<PERSON><PERSON> tác thành công", "care_staff": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>c", "search_placeholder": "<PERSON><PERSON>, thông tin khách hàng", "used_to_take_care": "<PERSON><PERSON> liệu tôi từng chăm sóc", "only_me": "<PERSON><PERSON> liệu của tôi", "exclude_me": "<PERSON><PERSON> liệu của ngư<PERSON>i khác trừ tôi", "beginAt": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu", "endAt": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc", "empty_data": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "created_time": "THỜI GIAN TẠO", "select_marketer": "Chọn marketer", "select_care_staff": "<PERSON><PERSON><PERSON> ng<PERSON> phụ trách", "delete": "Xóa", "name_the_filter_and_press_enter_to_save": "<PERSON><PERSON><PERSON><PERSON> tên bộ lọc và nhấn Enter để lưu", "apply": "<PERSON><PERSON>", "save_and_close": "<PERSON><PERSON><PERSON>", "save_and_apply": "<PERSON><PERSON><PERSON> và <PERSON> dụng", "appointment_schedule": "<PERSON><PERSON><PERSON> hẹn", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "select_projects": "<PERSON><PERSON><PERSON> d<PERSON>n", "project": "<PERSON><PERSON> án", "facebook_page": "Trang Facebook", "landing_page": "Landing page", "source": "<PERSON><PERSON><PERSON><PERSON>", "latestUpdate": "<PERSON><PERSON><PERSON> nh<PERSON>t gần nhất", "latestReason": "<PERSON><PERSON><PERSON> s<PERSON> g<PERSON> n<PERSON>t", "clear": "Đặt lại"}, "validate": {"appointment_time_is_required": "<PERSON><PERSON><PERSON><PERSON> gian hẹn là bắt buộc", "content_is_required": "<PERSON><PERSON><PERSON> dung lịch hẹn là bắt buộc", "appointment_content": "<PERSON><PERSON><PERSON> dung lịch hẹn", "send_result": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>n hồi", "please_select_a_care_result": "Bạn cần chọn kết quả phản hồi"}, "lead": {"mask_failed": "Bạn cần đ<PERSON>h dấu đơn này không bị trùng trướ<PERSON> khi phản hồi kết quả", "update_care_result": "Phản hồi kết quả", "select_result": "<PERSON><PERSON><PERSON> kết quả", "select_tag": "<PERSON><PERSON><PERSON> thẻ", "note_for_customer": "<PERSON><PERSON><PERSON> ý thêm về khách này", "care_histories": "<PERSON><PERSON><PERSON> sử chăm s<PERSON>c", "caring_staff": "<PERSON><PERSON>ân viên ch<PERSON>m s<PERSON>c", "schedule": "<PERSON><PERSON><PERSON> hẹn", "duplicate": "<PERSON><PERSON><PERSON> b<PERSON>n", "remove": "Xóa", "create": "Tạo", "cancel": "<PERSON><PERSON><PERSON>", "called": "Đã gọi", "missed": "Đã nhỡ", "created_appointment_schedule_successfully": "<PERSON><PERSON><PERSON> lịch hẹn thành công", "removed_appointment_schedule_successfully": "<PERSON><PERSON><PERSON> lịch hẹn thành công", "mark_as_called_successfully": "<PERSON><PERSON><PERSON> dấu đã gọi thành công", "you_are_sure_remove_appointment_schedule": "Bạn chắc chắn muốn xóa lịch hẹn", "Close_the_lead": "<PERSON><PERSON><PERSON> lead", "Does_the_customer_have_a_delivery_date": "<PERSON><PERSON><PERSON>ch có hẹn ngày giao hàng không?", "Customers_scheduled_delivery_date": "Khách hẹn ngày giao", "Customers_want_to_receive_their_orders_immediately": "<PERSON><PERSON><PERSON><PERSON> hàng muốn nhận hàng ngay ", "Close_orders_now": "<PERSON><PERSON><PERSON> đơn ngay", "Create_new_reminder_to_remind_the_delivery_date_or_call_back_the_customer": "Đặt lịch hẹn để nhắc ngày gửi đơn hoặc liên hệ lại với khách ", "The_preferred_receiving_date": "<PERSON><PERSON><PERSON> <PERSON>n mong muốn", "Select_date": "Select date", "The_scheduled_call-back_date": "Ngày hẹn gọi ", "Scheduled_time_24_hours": "Giờ hẹn (24h) ", "Select_time": "Select time", "Notes_Required": "<PERSON><PERSON>i dung hẹn (Bắt buộc)", "When_pressing_the_button_below": "<PERSON><PERSON> nhấn nút bê<PERSON>, Lead sẽ đượ<PERSON> chốt trướ<PERSON>, đơn hàng chưa được gửi đi. Bạn cần phải vào lại Lead này để xác nhận đơn hàng khi đến ngày gửi hàng. ", "Create_reminder_and_close_the_order_temporarily.": "<PERSON><PERSON><PERSON> l<PERSON>ch hẹn và chốt lead tạm", "Create_reminder_and_close": "<PERSON><PERSON><PERSON> l<PERSON><PERSON> nhắc và chốt tạm . Quay lại xác nhận đơn khi đến ngày hẹn.", "Create_reminder_and_close_the_order_temporarily": "<PERSON><PERSON><PERSON> l<PERSON><PERSON> nhắc và chốt tạm. Quay lại xác nhận đơn khi đến ngày hẹn."}, "history": {"created": "<PERSON><PERSON><PERSON><PERSON> mới", "updated": "<PERSON><PERSON><PERSON>", "reason": "Lý do", "note": "<PERSON><PERSON><PERSON>", "tag": "Thẻ", "changed_state": "Chuyển trạng thái chăm sóc", "times": "<PERSON><PERSON>n thứ {{count_number}}", "create_schedule": "<PERSON><PERSON><PERSON> l<PERSON> hẹn", "mark_called": "<PERSON><PERSON><PERSON> dấu đã g<PERSON>i", "remove_schedule": "<PERSON><PERSON><PERSON> l<PERSON> hẹn", "person_in_charge": "Ngườ<PERSON> phụ trách", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "view_schedule_missed": "Xem {{total}} lịch hẹn đã qua", "ignore_duplicate_warning": "Bỏ qua cảnh bảo trùng", "remove_duplicate_warning": "Gỡ cảnh báo trùng ", "with": "với "}, "reason": {"cannot_reach": "<PERSON><PERSON><PERSON><PERSON> thể gọi", "mind_changing": "Đ<PERSON>i ý", "urgent_problem": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>p", "no_money": "<PERSON><PERSON><PERSON> t<PERSON>", "duplicate_order": "<PERSON><PERSON><PERSON><PERSON>n", "test_order": "Đơn test của MKT", "wrong_phone_number_format": "<PERSON><PERSON> điện thoại sai định dạng", "product_does_not_meet_expectation": "<PERSON><PERSON><PERSON> ph<PERSON>m không n<PERSON><PERSON> muốn", "awaiting_confirmation": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> chốt", "wrong_owner": "<PERSON><PERSON><PERSON><PERSON> phải chủ nhân thuê bao", "price_factor": "<PERSON><PERSON><PERSON>t bại do giá", "wrong_number": "<PERSON>", "call_back_later": "<PERSON><PERSON>, hẹn lại", "no_response": "<PERSON><PERSON><PERSON><PERSON> nghe máy", "confirmed": "<PERSON><PERSON><PERSON> đ<PERSON>n", "purchase_elsewhere": "<PERSON><PERSON> b<PERSON><PERSON>", "accidental_purchase": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>m nh<PERSON>m", "joking": "<PERSON><PERSON><PERSON><PERSON> trêu/ Bom", "blank": "Chưa gắn lý do"}, "config": {"collect_type": "Ưu tiên nhận data được thu thập theo phương thức", "quota": "<PERSON><PERSON><PERSON> m<PERSON>c", "Quota_configuration": "<PERSON><PERSON><PERSON> hình đ<PERSON>nh mức", "Specify_quotas_for_the_system_to_automatically_retrieve_or_allocate_data_for_personnel": "Quy định các định mức cho hệ thống tự động thu hồi hoặc phân bổ dữ liệu cho nhân sự", "Specify_quotas_for_the_system_to_automatically_retrieve_or_allocate_data_for_personnel_for_as": "Quy định các định mức cho hệ thống tự động thu hồi  hoặc tự chuyển trạng thái dữ liệu khi vượt định mức chăm sóc", "assigned_leads": "Đối với data Đ<PERSON> tiếp nhận", "maxLeads": "Số data được giữ tối đa", "maxLeadKeep": "Số data được giữ tối đa", "Unlimited": "<PERSON><PERSON><PERSON>ng gi<PERSON>i hạn ", "Maximum": "<PERSON><PERSON><PERSON> đa", "data": "data", "returnLeadsAt": "<PERSON><PERSON><PERSON><PERSON> gian thu hồi", "Revoke_at_the_end_of_the_shift": "<PERSON>hu hồi vào cuối ca ", "Revoke_after_a_specific_time_mark": "<PERSON><PERSON> hồi sau mốc thời gian ", "processing_leads": "Đối với data <PERSON><PERSON> xử lý", "The_maximum_number_of_data_can_be_kept": "Số <PERSON> đ<PERSON><PERSON><PERSON> giữ tối đa", "Data": "Data", "returnNoAttemptedAt": "Thời gian thu hồi data Ch<PERSON>a chăm sóc (No Attempt)", "returnAttemptedAt": "Thời gian thu hồi data Đã chăm sóc (Attempted)", "returnPotentialAt": "Thời gian thu hồi data Tiềm năng (Potential)", "Save": "<PERSON><PERSON><PERSON>", "no_response": "<PERSON><PERSON><PERSON><PERSON> nghe máy", "Busy_call_back_later": "<PERSON><PERSON>, hẹn lại", "Awaiting_confirmation": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> chốt", "times": "<PERSON><PERSON><PERSON>", "assigned_and_in_progress": "<PERSON><PERSON> tiếp nhận và <PERSON>ang xử lý", "No Attempt": "<PERSON><PERSON><PERSON> ch<PERSON>m sóc (No Attempt)", "processing_leads_note": "Số data Ch<PERSON>a chăm sóc (No Attempt) được giữ tối đa", "assigned": "Assigned", "minute": "<PERSON><PERSON><PERSON>", "failed_leads": "Đối với data Thất bại", "mark_as_failed_when_tries_reached_n_times": "Data sẽ bị đánh dấu Thất bại khi đã cố gắng chăm sóc bao nhiêu lần", "mark_as_canceled_when_failure_reached_n_times": "Data sẽ bị đánh dấu Hủy khi bị Thất bại bao nhiêu lần", "successfully": "<PERSON><PERSON> tác thành công", "min_limit_data": "Số data được nhận tối thiểu", "coefficient_new": "Hệ số data mới sau mỗi lần yêu cầu (có cộng dồn)", "average_proceed": "Thời gian xử lý trung bình/ data", "mark_captured_data_as_failed_when_tries_reached_n_times": "Data Capture Form sẽ bị đánh dấu Thất bại khi đã cố gắng chăm sóc bao nhiêu lần", "mark_facebook_conversion_data_as_failed_when_tries_reached_n_times": "Data Facebook Conversion sẽ bị đánh dấu Thất bại khi đã cố gắng chăm sóc bao nhiêu lần", "for_manual_data": "Đối với data Manual Keying", "for_captured_data": "Đối với data Capture Form", "for_fb_conversion_data": "Đối với data Facebook Conversion", "markPotentialDataAsFailed": "Data Tiềm năng (Potential) sẽ bị đánh dấu Thất bại khi cập nhật lý do <PERSON> sắp chốt bao nhiêu lần", "no_withdrawal": "<PERSON><PERSON><PERSON><PERSON> thu hồi"}, "Processing Procedure": "<PERSON><PERSON> trình xử lý", "Request for more data": "<PERSON><PERSON><PERSON> c<PERSON>u thêm dữ liệu", "Operation history": "<PERSON><PERSON><PERSON> sử thao tác", "Regulations on data distribution": "Quy định phân phối data", "When personnel actively request more data": "Khi nhân sự chủ động yêu cầu thêm data", "Regulations on how to distribute data when personnel actively request more data to care": "Quy định cách thức phân phối data khi nhân sự chủ động yêu cầu thêm data để chăm sóc", "Link illustrating the results that personnel will receive with the settings next to it": "Link minh họa kết quả mà nhân sự sẽ nhận được với cài đặt bên cạnh", "(On average, how long does it take for an personnel to process 1 data? (Eg: 3.5 minutes, 7 minutes...)": "(Trung bình 1 nhân sự xử lý xong 1 data trong bao lâu? (VD: 3.5 phút, 7 phút...)", "Minutes": "<PERSON><PERSON><PERSON>", "(In a request for more data, personnel will receive at least ...)": "(Trong 1 lần yêu cầu thêm data, nhân sự sẽ được nhận tối thiểu là...)", "(The formula to calculate the percentage of new data is as follows [Number of requests] x [Coefficient] * 100) For example: Assuming the coefficient is 0.2 and the personnel requests more data 3 times, at each request 1, 2 and 3, personnel will receive 20%, 40% and 60% of new data, respectively, of the total data received": "(Công thức tính tỉ lệ % data mới như sau [Số lần yêu cầu] x [Hệ số] * 100) VD: Giả sử hệ số là 0.2 và nhân sự yêu cầu thêm data 3 lần thì ở mỗi lần yêu cầu 1, 2 và 3 nhân sự sẽ lần lượt nhận được lần lượt là 20%, 40% và 60% data mới trong tổng số data được nhận", "Coefficient": "<PERSON><PERSON> s<PERSON>", "schedule": {"today": "<PERSON><PERSON><PERSON> nay", "upComing": "<PERSON><PERSON><PERSON> t<PERSON>i", "missed": "Đã nhỡ", "have_you_called_customer_yet": "Bạn đã gọi cho khách?", "mark_as_called": "Đã gọi", "no_response": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "mask_as_called_successfully": "<PERSON><PERSON><PERSON> dấu đã gọi thành công"}, "No lead to gather!": "<PERSON><PERSON><PERSON><PERSON> có <PERSON> để thu hồi", "Procedure": "<PERSON><PERSON> tr<PERSON>nh", "Settings for data processing procedures": "<PERSON>ài đặt liên quan đến quy trình xử lý dữ liệu", "For Assigned data": "<PERSON><PERSON><PERSON> với dữ liệu Đ<PERSON> tiếp nhận", "Automatically move data to In Process - No Attempt": "Tự động chuyển dữ liệu qua bước <PERSON>ang xử lý - Chưa xử lý", "Gather data types: Manual Keying, Facebook Conversion, Capture Form": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>c lo<PERSON>i dữ liệu: Manual Keying, Facebook Conversion, Capture Form", "Only Gather data types: Manual Keying, Converted from Facebook": "Chỉ nhận loại dữ liệu: <PERSON><PERSON><PERSON><PERSON><PERSON>, từ <PERSON><PERSON><PERSON>", "Only gather Capture Form data": "Chỉ nhận mỗi loại dữ liệu Capture Form", "success": "Thay đổi Collect Method thành công", "Select reason": "Chọn lý do", "Get Data": "<PERSON><PERSON><PERSON> d<PERSON> liệu", "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "time_updated": "{{time}}", "Chốt đơn": "<PERSON><PERSON><PERSON> đ<PERSON>n", "Không nghe máy": "<PERSON><PERSON><PERSON><PERSON> nghe máy", "Đang bận, hẹn lại": "<PERSON><PERSON>, hẹn lại", "Khách sắp chốt": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> chốt", "Không thể gọi": "<PERSON><PERSON><PERSON><PERSON> thể gọi", "Sai số": "<PERSON>", "Không phải chủ nhân thuê bao": "<PERSON><PERSON><PERSON><PERSON> phải chủ nhân thuê bao", "Thất bại do giá": "<PERSON><PERSON><PERSON>t bại do giá", "Đổi ý": "Đ<PERSON>i ý", "Mua bên khác": "<PERSON><PERSON> b<PERSON><PERSON>", "Khách bấm nhầm": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>m nh<PERSON>m", "Khách trêu/ Boom": "<PERSON><PERSON><PERSON><PERSON> trêu/ <PERSON>", "Khẩn cấp": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>p", "Hết tiền": "<PERSON><PERSON><PERSON> t<PERSON>", "Sản phẩm không như mong muốn": "<PERSON><PERSON><PERSON> ph<PERSON>m không nh<PERSON> mong muốn", "Trùng đơn": "<PERSON><PERSON><PERSON><PERSON>n", "Đơn test của MKT": "Đơn test của MKT", "Số điện thoại sai định dạng": "<PERSON><PERSON> điện thoại sai định dạng", "All": "<PERSON><PERSON><PERSON> c<PERSON>", "Created": "Khởi tạo lúc", "system": "<PERSON><PERSON> th<PERSON>", "assignee": "<PERSON><PERSON><PERSON> ng<PERSON> phụ trách", "page_group": "Nhóm Page", "Only gather Facebook Conversion data": "Chỉ nhận mỗi loại dữ liệu từ Facebook Conversion", "Only gather Manual Keying data": "Chỉ nhận mỗi loại dữ liệu Manual Keying", "Called phone number": "<PERSON><PERSON> gọi cho số điện thoại", "No data available": "Chưa có dữ liệu", "Note": "<PERSON><PERSON><PERSON>", "Address": "Địa chỉ", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Canceling a lead will prevent the user from further action on the lead. Are you sure you want to continue?": "Vi<PERSON><PERSON> huỷ lead sẽ khiến user không thể thao tác thêm với lead. <PERSON><PERSON><PERSON> có chắc chắn muốn tiếp tục không?", "merged_orders": "<PERSON><PERSON><PERSON>", "duplicate_leads": "Tr<PERSON><PERSON> dữ liệu", "Change value": "Thay đổi giá trị", "minutes": "<PERSON><PERSON><PERSON><PERSON>", "Actor": "<PERSON><PERSON><PERSON><PERSON> thao tác", "Location change": "<PERSON><PERSON> trí thay đổi", "Content": "<PERSON><PERSON>i dung", "Time": "<PERSON><PERSON><PERSON><PERSON> gian", "Gather data types": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>c lo<PERSON>i dữ liệu", "Only gather": "Chỉ nhận mỗi loại dữ liệu", "Carepage Configuration": "<PERSON><PERSON><PERSON>page", "Drag or drop file here to create multiple datas": "Kéo hoặc thả tệp vào khu vực này để tạo nhiều dữ liệu", "valid data(s)": "dữ liệu sẵn sàng để xử lý", "invalid data(s)": "dữ liệu không đủ điều kiện", "Import this file": "<PERSON><PERSON><PERSON><PERSON> liệu", "Close": "Đ<PERSON><PERSON>", "Import successfully": "<PERSON><PERSON><PERSON><PERSON> dữ liệu thành công", "Or": "Hoặc", "Download error data": "<PERSON><PERSON><PERSON> dữ liệu lỗi", "Select All": "<PERSON><PERSON><PERSON> tất cả", "Deselect All": "Bỏ chọn tất cả"}