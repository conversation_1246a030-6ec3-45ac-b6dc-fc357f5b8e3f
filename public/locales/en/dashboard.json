{"ticket": {"project": "Project", "projectName": "Search project name", "totalTickets": "Total Tickets", "claim": "<PERSON><PERSON><PERSON>", "warehouse": "Warehouse", "export": "Export", "clear": "Clear", "total": "Total", "selectFilter": "Select Filter", "createFilter": "Create filter", "default": "<PERSON><PERSON><PERSON>", "filter": "Filter", "moreFilter": "More Filters", "savedFilter": "Saved Filters", "date": "Date", "received": "Received Tickets", "resolved": "Resolved Tickets", "proceedOrder": "Proceed Orders", "returned": "Returned", "processedDelivered": "Delivered (Processed)", "unprocessedDelivered": "Delivered (Unprocessed)", "returnRate": "Return Rate", "carrier": "Carrier", "client": "Client", "product": "Product", "clientName": "Client Name", "productName": "Product Name", "carrierName": "Carrier Name", "convertRate": "Conversion Rate", "remove": "Are you sure remove {{name}} filter?", "removeContent": "This action cannot be undone.", "handoverOrders": "Handover Orders", "proceedOrders": "Processed Orders", "raisedTickets": "Raised Tickets", "uncounted": "Uncounted Tickets", "unprocessedReturned": "Returned (Unprocessed)", "processedReturned": "Returned (Processed)", "lateProcessed": "Returned (Late Processed)", "unprocessed": "Delivered (Unprocessed)", "deliveredNotified": "Delivered (Notified Delivery)", "deliveredRescued": "Delivered (Rescued)", "assignedTickets": "Assigned Tickets", "Unprocessed": "Unprocessed", "processing": "Processing", "Resolved": "Resolved", "rushDelivery": "<PERSON> Delivery", "notifyDelivery": "Notify Delivery", "retryDelivery": "Retry Delivery", "ticketByDate": "Ticket By Dates", "performanceByDate": "Rescue Rate by Dates", "ticketByStaff": "Ticket By Staff", "performanceByStaff": "Rescue Rate By Staff", "staff": "Staff", "Unprocess": "Unprocessed", "AwProcess": "Aw Process", "Processing": "Processing", "NO": "NO."}, "staffAssignDesc": {"tickets": "<p>Total tickets are assigned for staff and their status belongs to status: Assigned, In Process, Follow up, Resolved", "assigned": "<p>Total tickets assigned to a specific staff are currently in the <strong>Assigned</strong> status.</p>", "inProcess": "<p>Total tickets assigned to a specific staff are currently in the <strong>In process</strong> status.</p>", "followUp": "<p>Total tickets assigned to a specific staff are currently in the <strong>Follow up</strong> status.</p>", "resolved": "<p>Total tickets assigned to a specific staff are currently in the <strong>Resolved</strong> status.</p>"}, "staffAssignTitle": {"tickets": "Tickets", "assigned": "Assigned", "inProcess": "In Process", "followUp": "Follow Up", "resolved": "Resolved", "performance": "Performance", "staffAssignment": "Staff Assignment"}, "kindTicket": {"overview": "Overview", "viewByDate": "View by Date", "viewByStaff": "View by Staff", "performance": "Performance", "staffAssignment": "Staff Assignment"}, "overviewDesc": {"ProcessedOrders": "Total processed orders that were raised tickets.", "TotalTickets": "<p>Total created ticket and their status belongs to one of the statuses: <strong>New, Reopen, Assigned, In Process, Follow up, Resolved</strong> <i>(excluding Closed)</i></p>", "AssignedTickets": "<p>Total tickets that their status belongs to one of the statuses: <strong>Assigned, In Process, Follow up, Resolved</strong>.</p>", "Unprocessed": "<p>Total unprocessed tickets that meet both two of the following conditions:</p> <ul> <li>Ticket status: <strong>Resolved</strong>,</li> <li>No selected result.</li> </ul> <p>Rate (%) = Unprocessed Tickets / Total Tickets * 100</p>", "AwProcess": "<p> Total tickets are in the <strong>Assigned</strong> status. </p> <p>Rate (%) = Aw Process Tickets / Total Tickets * 100</p>", "Processing": "<p> Total tickets are in the <strong>In Process</strong> or <strong>Follow up</strong> status and have at least 01 selected result(s). </p> <p>Rate (%) = Processing Tickets / Total Tickets * 100</p>", "Resolved": "<p> Total tickets are in the <strong>Resolved</strong> status. </p> <p>Rate (%) = Resolved Tickets / Total Tickets * 100</p>"}, "viewByDateDesc": {"ProcessedOrders": "<p><PERSON><PERSON> lượng đơn hàng phát sinh ticket trong thời gian lọc</p><p>Nếu đơn được phát sinh từ 02 ticket trở lên → vẫn tính là 01 đơn hàng</p><p>Nếu đơn phát sinh ticket ở nhiều ngày → vẫn tính là 01 đơn hàng</p>", "TotalTickets": "Sum các giá trị ở cột tương ứng\nRate 4 cột Unprocessed, Aw Process, Processing, Resolved vẫn áp dụng công thức của từng giá trị, ví dụ:\nRate Unprocessed (%) = ∑Unprocessed/∑Total Tickets*100", "AssignedTickets": "", "Unprocessed": "", "AwProcess": "", "Processing": "", "Resolved": ""}, "staffAssign": {"NO": "NO", "staff": "Staff", "tickets": "Tickets", "assigned": "Assigned", "inProcess": "In Process", "followUp": "Follow Up", "resolved": "Resolved"}, "label": {"TicketType": "Ticket Type", "returnReason": "Return Reason", "orderTracker": "Order Tracker", "rescueRate": "Rescue Rate", "sellable": "Sellable", "damaged": "Lost, Damaged", "purpose": "Purpose", "inventories": "Inventories", "actors": "Actors", "remarks": "Remarks", "days": "Days", "close": "Close"}, "returnReason": {"createOrder": "Order Creation Time", "createWaybill": "Waybill Creation Time", "returnByPTStaff": "Return by PT Staff", "returnByClients": "Return by Clients", "returnByProducts": "Return by Products", "returnByCarriers": "Return by Carriers", "returnByRegions": "Return by Regions", "returnByDetails": "Return by Details", "returnByReason": "Return By Reason", "pTStaff": "PT Staff", "client": "Client", "Product": "Product", "Carrier": "Carrier", "Region": "Region", "ClientAndStaff": "Client & Staff"}, "orderTracker": {"orderCreationTime": "Order Creation Time", "waybillCreationTime": "Waybill Creation Time", "carrierAndClient": "Carriers & Clients", "regions": "Regions", "region": "Region", "regionName": "Region Name", "products": "Products", "orders": "Orders", "pickedUpOrders": "Picked Up Orders", "deliveryRate": "Delivery Rate", "returnRate": "Return Rate", "orderByRegion": "Orders by Region", "carriers": "Carriers", "clients": "Clients", "quantity": "Quantity", "topDeliveryRateByRegion": "Top Delivery Rates by Region", "topReturnRateByRegion": "Top Return Rates by Region", "outForDelivery": "Out for Delivery", "delivered": "Delivered", "returned": "Returned", "province": "Province", "category": "Category", "productByCategory": "Products by Categories", "saleByProduct": "Sales by Products", "grandTotal": "Grand Total", "3plPickedUp": "3PL Picked Up", "inDelivery": "In Delivery", "totalPickedUp": "Total Picked Up", "totalAssigned": "Total Assigned", "inboundProcessing": "Inbound Processing", "totalOrders": "Total orders", "filter": "Filter", "selectFilter": "Select Filter", "createFilter": "Create Filter", "apply": "Apply", "saveThenClose": "Save filter then close", "saveThenApply": "Save filter then apply", "clear": "Clear", "advancedFilter": "Advanced Filter", "unassignCarrier": "Unassign Carrier", "orderAssigned": "orders not assigned to carrier", "filterNamePlaceholder": "Type the filter name then press enter", "total": "Total"}, "orderShipment": {"overview": "Overview", "carriers": "Carriers", "sale_reps": "Sales Reps", "date": "Date", "total_order": "Total Order", "summary": "Summary", "save_as_excel": "Save as Excel", "overview_dashboard": {"new": "New", "order_created": "Total Orders", "order_awaiting": "Awaiting Stock Orders", "order_confirmed": "Confirmed Orders", "order_handled": "Handed Over Orders", "order_in_delivery": "In-Delivery Orders", "order_delivered": "Delivered Orders", "order_returned": "Returned Orders", "order_canceled": "Canceled Orders"}, "sale_and_revenue": {"sale_and_revenue": "Sale & Revenue", "actual_revenue": "Revenue", "expected_revenue": "Expected Sales", "new_sale": "New Sales", "returned_sale": "Returned Sales", "canceled_sale": "Canceled Sales"}, "sale_by_project": {"sale_by_project": "Sale by Project", "view_sales_and_revenue": "View Sales & Revenue", "view_actual_revenue": "View Actual Revenue"}, "order_by_product": {"selling_product": "Selling products", "order_by_product": "Order By Products", "delivered_product": "Delivered Products", "returned_product": "Returned Products", "category": "Categories", "sku": "SKU", "product_name": "Product Name", "properties": "Properties", "selling_price": "Selling <PERSON>", "selling_quantity": "<PERSON><PERSON>", "total_order": "Total Orders"}, "order_by_date": {"detail": "Detail", "order_by_date": "Order By Date", "sku_qtt": "SKU Qtt", "avg_value": "Avg. Order Value", "sale_and_revenue": "Sale & Revenue", "returned_sale": "Returned Sales", "expected_revenue": "Expected Sales", "actual_revenue": "Revenue", "sale": "Sale Revenue", "total_order": "Total Order", "Asc": "Click to sort ascending", "Desc": "Click to sort descending", "CancelSort": "Click to cancel sorting"}, "shipment_by_carrier": {"shipment_by_carrier": "Shipment by Carriers", "shipment_by_date": "Shipment by Handover Dates", "Asc": "Click to sort ascending", "Desc": "Click to sort descending", "CancelSort": "Click to cancel sorting"}, "shipment_by_sale_reps": {"sale_reps": "Top Sales Reps.", "SalesReps": "Order by Sales Reps", "total_order": "Total Orders", "Awaiting Stock": "Awaiting Stock", "Confirmed": "Confirmed", "Canceled": "Canceled", "Handed Over": "Handed Over", "In-Delivery": "In-Delivery", "Delivered": "Delivered", "Returned": "Returned", "SKUQtt": "SKU Qtt", "AvgOrderValue": "Avg. Order Value", "SaleRevenue": "Sale Revenue", "ReturnedSales": "Returned Sales", "ExpectedSales": "Expected Sales", "Revenue": "Revenue", "Summary": "Summary", "save_as_excel": "Save as Excel", "Sale": "Sales", "Number": "Sort By Number", "Precent": "Sort By Percent", "Asc": "Click to sort ascending", "Desc": "Click to sort descending", "CancelSort": "Click to cancel sorting", "Project": "Project", "New": "New"}, "avg_order_value": "Avg. Order Value", "filter": {"today": "Today", "yesterday": "Yesterday", "this_week": "This Week", "last_week": "Last Week", "this_month": "This Month", "last_month": "Last Month", "project": "Project", "select_project": "Select Project", "sale_reps": "Sales Reps", "select_sales_reps": "Select Sales Reps.", "product": "Product", "select_products": "Select Products", "all_type": "All Types", "type": "Types", "all_source": "All Sources", "sources": "Sources", "select_source": "Select Sources", "sources_type": "Sources Type", "select_source_type": "Select Source Type", "other": "Other", "marketer": "Marketers", "carrier": "Carriers", "Search": "Search....", "CreateFilter": "Create Filter", "Input": "Input group name and press enter", "Close": "Close", "Savefilter": "Save filter", "CreationTime": "Creation Time", "ConfirmationTime": "Confirmation Time", "HandoverTime": "Handover Time", "Tags": "Tags", "Select All": "Select All"}, "carrier": {"handover": "Total Handed Over", "delivered": "Delivered", "returned": "Returned", "created_orders": "Created Orders", "handover_orders": "Handed Over Orders", "in_delivery_orders": "In-Delivery Orders", "delivered_orders": "Delivered Orders", "returned_orders": "Returned Orders", "total_assigned": "Total Assigned"}, "sales": {"created": "Created Orders", "delivered": "Delivery Orders", "returned": "Returned Order", "deliveredRate": "Delivery Rate", "returnedRate": "Returned Rate"}}, "telesales": {"filter": {"title": "telesales", "today": "Today", "yesterday": "Yesterday", "this_week": "This Week", "last_week": "Last Week", "this_month": "This Month", "last_month": "Last Month", "project": "Project", "select_project": "Select Project", "sale_reps": "Sales Reps", "select_sales_reps": "Select Sales Reps.", "product": "Product", "select_products": "Select Products", "all_type": "All Types", "type": "Types", "all_source": "All Sources", "sources": "Sources", "select_source": "Select Sources", "sources_type": "Sources Type", "select_source_type": "Select Source Type", "other": "Other", "marketer": "Marketers", "carrier": "Carriers", "CollectMethod": "Collect Method", "CreationTime": "Creation Time", "ProcessingTime": "Processing Time", "Lead Stages": "Lead Stages", "LeadStage": "Lead Stages", "All": "All Pages"}, "overview_dashboard": {"Overview": "Overview", "total_leads": "Total Leads", "confirmed_leads": "Confirmed Leads", "ConfirmedRate": "Confirmed Rate", "sale_and_revenue": "Sales", "aov": "AOV", "combo_rate": "Combo Rate", "Unassigned": "Unassigned", "Assigned": "Assigned", "In-Process": "In-Process", "Confirmed": "Confirmed", "Canceled": "Canceled", "Processable": "Processable", "actual_revenue": "Revenue", "expected_revenue": "Expected Sales", "new_sale": "New Sales", "returned_sale": "Returned Sales", "canceled_sale": "Canceled Sales"}, "SalesbyDates_dasboard": {"ProcessedLeads": "Processed Leads", "SalesRevenue": "Sales Revenue"}, "LeadbyProducts": "Lead by Products", "Overview": "Overview", "CVRbyProjects": "CVR by Projects", "SalesbyDates": "Sales by Dates", "Summary": "Summary", "Performance": "Performance", "CareReasons": "Care Reasons", "Savefilter": "Save filter", "ConfirmedRate": "Confirmed Rate", "Search": "Search....", "CreateFilter": "Create Filter", "Input": "Input group name and press enter", "Close": "Close", "overviewDetail": {"Date": "Date", "Processable": "Processable", "TotalLead": "Total Lead", "ProcessedLeads": "Processed Leads", "ConfirmedLeads": "Confirmed Leads", "CanceledLeads": "Canceled Leads", "SKUQtt": "SKU Qtt", "DeliveredOrders": "Delivered Orders", "ReturnedOrders": "Returned Orders", "SalesRevenue": "Sales", "aov": "AOV", "ComboRate": "Combo Rate", "Number": "Sort By Number", "Precent": "Sort By Percent", "Details": "Details", "save_as_excel": "Save as Excel", "Summary": "Summary", "TotalProcessed": "Total Processed", "notFound": "Not found", "Asc": "Click to sort ascending", "Desc": "Click to sort descending", "CancelSort": "Click to cancel sorting", "Project": "Project", "Sale": "Sales", "Products": "Products"}, "overview_performance": {"Overview": "Overview ", "TotalLead": "Total Lead", "Unassigned": "Unassigned", "Assigned": "Assigned", "In-Process": "In-Process", "Confirmed": "Confirmed", "Canceled": "Canceled", "PerformanceByDate": {"Summary": "Summary", "Total Assigned": "Total Assigned", "Total Confirmed": "Total Confirmed", "Total Processed": "Total Processed", "Confirmed Rate": "Confirmed Rate", "New Assigned": "Assigned New Lead", "New Processed": "Processed New Lead", "Old Assigned": "Assigned Old Lead", "Old Processed": "Processed Old Lead", "New Messages With Reasons": "New Lead With Reasons", "Old Messages With Reasons": "Old Lead With Reasons", "Date": "Date", "Sale Reps.": "Sale Reps.", "Processed New Message": "Processed New Lead", "Confirmed New Message": "Confirmed New Lead", "Processed Old Message": "Processed Old Lead", "Confirmed Old Message": "Confirmed Old Lead", "Sales Revenue": "Sales Revenue", "Revenue": "Revenue", "New": "New", "Old": "Old", "PerformancebyDate": "Performance by Date", "Number": "Sort By Number", "Precent": "Sort By Percent", "save_as_excel": "Save as Excel", "TotalProcessed": "Total Processed", "ReturnedOrders": "Returned Orders", "Asc": "Click to sort ascending", "Desc": "Click to sort descending", "CancelSort": "Click to cancel sorting", "PerfomancebySaleReps": "Perfomance by Sale", "AOV": "AOV", "Returned Orders": "Returned Orders", "Return Rate": "Return Rate", "Delivered Orders": "Delivery Orders", "Delivery Rate": "Delivery Rate"}, "PerformancebyDate_performance": {"PerformancebyDate": "Performance by Date", "PerfomancebySaleReps": "Perfomance by Sale", "Sales": "Sales", "Date": "Date", "TotalLead": "Total Lead", "NewLeads": "New Leads ", "NewLeads_process": "New Leads (Processed)", "Confirmation": "Confirmation", "OldLeads": "Old Leads", "OldLeads_processed": "Old Leads (Processed)", "Confirmation_old": "Confirmation", "SalesRevenue": "Sales Revenue", "Revenue": "Revenue", "Number": "Sort By Number", "Precent": "Sort By Percent", "save_as_excel": "Save as Excel", "Summary": "Summary", "TotalProcessed": "Total Processed", "ReturnedOrders": "Returned Orders", "Asc": "Click to sort ascending", "Desc": "Click to sort descending", "CancelSort": "Click to cancel sorting"}, "CVR": {"top5": "View top 5 ", "top10": "View top 10  ", "top15": "View top 15 nhân viên ", "New": "CVR on New Data by Sales Reps.", "Old": "CVR on Old Data by Sales Reps.", "Conversion New Rate": "Conversion New Rate", "Conversion Old Rate": "Conversion Old Rate", "Processed New Leads": "Processed New Leads", "Processed Old Leads": "Processed Old Leads"}, "Status": {"PerformancebyStatus": "Performance by Status", "Sale": "Sales", "Total Assigned": "Total Assigned", "Assigned": "Assigned", "In process - No Attempt": "In Process- No Attempt", "In process - Attempted": "In Process - Attempted", "In process - Not Connected": "In Process - Not Connected", "In process - Connected": "In Process - Connected", "In process - Potential": "In Process-Potential", "Confirmed": "Confirmed", "Failed": "Failed", "Cancel": "Cancel", "Summary": "Summary", "Number": "Sort By Number", "Precent": "Sort By Percent", "Asc": "Click to sort ascending", "Desc": "Click to sort descending", "save_as_excel": "Save as Excel", "CancelSort": "Click to cancel sorting"}}, "CareReasons_dashboard": {"CareReasons": "Care Reasons", "CareReasons_bydate": "Care Reasons By Dates", "Summary": "Summary", "Date": "Date", "TotalLeads": "Total Leads", "Totalreasons": "Total leads with reasons", "Number": "Sort By Number", "Precent": "Sort By Percent", "save_as_excel": "Save as Excel", "Sales": "Sales", "CareReasonsBySales": "Care Reasons By Sales", "Project": "Project", "CareReasonsByProjects": "Care Reasons By Projects", "Asc": "Click to sort ascending", "Desc": "Click to sort descending", "CancelSort": "Click to cancel sorting"}}, "care_page": {"filter": {"title": "telesales", "today": "Today", "yesterday": "Yesterday", "this_week": "This Week", "last_week": "Last Week", "this_month": "This Month", "last_month": "Last Month", "project": "Project", "select_project": "Select Project", "sale_reps": "Sales Reps", "select_sales_reps": "Select Sales Reps.", "product": "Product", "select_products": "Select Products", "all_type": "All Types", "type": "Types", "all_source": "All Sources", "sources": "Sources", "select_source": "Select Sources", "sources_type": "Sources Type", "select_source_type": "Select Source Type", "other": "Other", "marketer": "Marketers", "carrier": "Carriers", "CollectMethod": "Collect Method", "CreationTime": "Creation Time", "ProcessingTime": "Processing Time"}, "overview_dashboard": {"Overview": "Overview", "total_conversations": "Total Conversations", "confirmed_conversations": "Confirmed Conversations", "ConfirmedRate": "Confirmed Rate", "sale_and_revenue": "Sales Revenue", "average_order_value": "AOV", "combo_rate": "Combo Rate", "Unassigned": "Unassigned", "Assigned": "Assigned", "In-Process": "In-Process", "Confirmed": "Confirmed", "Canceled": "Canceled"}, "SalesbyDates_dasboard": {"ProcessedLeads": "Processed Conversations", "SalesRevenue": "Sales Revenue"}, "Processed New Message": "Processed New Message", "ConversationByProducts": "Conversation by Products", "Overview": "Overview", "CVRbyProjects": "CVR by Projects", "SalesbyDates": "Sales by Dates", "Summary": "Summary", "Performance": "Performance", "CareReasons": "Care Reasons", "Savefilter": "Save filter", "ConfirmedRate": "Confirmed Rate", "Search": "Search....", "CreateFilter": "Create Filter", "Input": "Input group name and press enter", "Close": "Close", "overviewDetail": {"Date": "Date", "TotalLead": "Total Lead", "ProcessedConversations": "Processed Conversations", "ConfirmedConversations": "Confirmed Conversations", "CanceledConversations": "Canceled Conversations", "SKUQtt": "SKU Qtt", "DeliveredOrders": "Delivered Orders", "ReturnedOrders": "Returned Orders", "SalesRevenue": "Sales Revenue", "aov": "AOV", "ComboRate": "Combo Rate", "Number": "Sort By Number", "Precent": "Sort By Percent", "Details": "Details", "save_as_excel": "Save as Excel", "Summary": "Summary", "TotalProcessed": "Total Processed", "Asc": "Click to sort ascending", "Desc": "Click to sort descending", "CancelSort": "Click to cancel sorting", "Project": "Project", "Sale": "Sales", "Products": "Products", "Comment": "Comment", "Total Comment": "Total Comment", "Total Messages": "Total Messages", "Phone Number from Cmt.": "Phone Number from Cmt.", "Message": "Message", "Processed Message": "Processed Message", "Phone Number from Mess.": "Phone Number from Mess.", "Confirmed Message": "Confirmed Message", "Rejected Message": "Rejected Message", "Orders": "Orders", "notFound": "No data"}, "overview_performance": {"Overview": "Overview ", "Total Conversations": "Total Conversations", "Unassigned": "Unassigned", "Assigned": "Assigned", "In-Process": "In-Process", "Confirmed": "Confirmed", "Canceled": "Canceled", "PerformanceByDate": {"Summary": "Summary", "Total Assigned": "Total Assigned", "Total Confirmed": "Total Confirmed", "Total Processed": "Total Processed", "Confirmed Rate": "Confirmed Rate", "New Assigned": "Assigned New Message", "New Processed": "Processed New Message", "Old Assigned": "Assigned Old Messages", "Old Processed": "Processed Old Messages", "New Messages With Reasons": "New Messages With Reasons", "Old Messages With Reasons": "Old Messages With Reasons", "Date": "Date", "Sale Reps.": "Sale Reps.", "Processed New Message": "Processed New Message", "Confirmed New Message": "Confirmed New Message", "Processed Old Message": "Processed Old Message", "Confirmed Old Message": "Confirmed Old Message", "Sales Revenue": "Sales Revenue", "Revenue": "Revenue", "New": "New", "Old": "Old", "PerformancebyDate": "Performance by Date", "Number": "Sort By Number", "Precent": "Sort By Percent", "save_as_excel": "Save as Excel", "TotalProcessed": "Total Processed", "ReturnedOrders": "Returned Orders", "Asc": "Click to sort ascending", "Desc": "Click to sort descending", "CancelSort": "Click to cancel sorting", "PerfomancebySaleReps": "Perfomance by Sale", "AOV": "AOV", "Returned Orders": "Returned Orders", "Return Rate": "Return Rate", "Delivered Orders": "Delivery Orders", "Delivery Rate": "Delivery Rate"}, "PerformancebyDate_performance": {"PerformancebyDate": "Performance by Date", "PerfomancebySaleReps": "Perfomance by Sale", "Sales": "Sales", "Date": "Date", "TotalLead": "Total Conversations", "NewConversations": "New Leads ", "NewLeads_process": "New Leads (Processed)", "Confirmation": "Confirmation", "OldLeads": "Old Leads", "OldLeads_processed": "Old Leads (Processed)", "Confirmation_old": "Confirmation", "SalesRevenue": "Sales Revenue", "Revenue": "Revenue", "Number": "Sort By Number", "Precent": "Sort By Percent", "save_as_excel": "Save as Excel", "Summary": "Summary", "TotalProcessed": "Total Processed", "ReturnedOrders": "Returned Orders", "Asc": "Click to sort ascending", "Desc": "Click to sort descending", "CancelSort": "Click to cancel sorting"}, "CVR": {"top5": "View top 5 ", "top10": "View top 10  ", "top15": "View top 15 nhân viên ", "New": "CVR on New Data by Sales Reps.", "Old": "CVR on Old Data by Sales Reps.", "Conversion New Rate": "Conversion New Rate", "Conversion Old Rate": "Conversion Old Rate", "Processed New Leads": "Processed New Leads", "Processed Old Leads": "Processed Old Leads", "Processed New Message": "Processed New Message", "Confirmed New Message": "Confirmed New Message", "Processed Old Message": "Processed Old Message", "Confirmed Old Message": "Confirmed Old Message", "new_leads_conversation_rate": ""}, "Status": {"PerformancebyStatus": "Perfomance by Status", "Sale Reps": "Sale Reps", "Assigned Message": "Assigned Message", "Assigned": "Assigned", "No Response": "No Response", "Processing": "Processing", "Potential": "Potential", "Confirmed": "Confirmed", "Rejected": "Rejected", "Summary": "Summary", "Number": "Sort By Number", "Precent": "Sort By Percent", "save_as_excel": "Save as Excel", "Asc": "Click to sort ascending", "Desc": "Click to sort descending", "CancelSort": "Click to cancel sorting"}}, "CareReasons_dashboard": {"CareReasons": "Care Reasons", "CareReasons_bydate": "Care Reasons By Dates", "Summary": "Summary", "Date": "Date", "TotalLeads": "Total Leads", "Totalreasons": "Total leads with reasons", "Number": "Sort By Number", "Precent": "Sort By Percent", "save_as_excel": "Save as Excel", "Sales": "Sales Revenue", "CareReasonsBySales": "Care Reasons By Sales", "Project": "Project", "CareReasonsByProjects": "Care Reasons By Projects", "Asc": "Click to sort ascending", "Desc": "Click to sort descending", "CancelSort": "Click to cancel sorting", "Total Messages": "Total Messages", "Messages with reasons": "Messages with reasons", "Sale Reps": "Sale Reps"}}, "rescueRate": {"totalAssigned": "Total Assigned", "handoverOrders": "Handover Orders", "proceedOrders": "Proceed Orders", "ReturnedRate": "Return Rate", "DeliveredRate": "Delivery Rate", "raisedTickets": "Raised Tickets", "uncounted": "Uncounted Orders", "Unprocessed": "Unprocessed", "Processed": "Processed", "LateProcessed": "Late Processed", "returned": "Returned", "delivered": "Delivered", "NotifyDelivery": "Notify Delivery", "Rescued": "Rescued"}, "sort": {"by": "Sort by", "value": "Number", "percentage": "Percentage"}, "view": {"view_top": "View top {{state}}"}, "inventory": {"beginning": "Beginning", "importDamaged": "Import Damaged", "supplier": "Supplier", "reimport": "Reimport", "stocktaking": "Stocktaking", "transfer": "Transfer", "others": "Others", "exportDamaged": "Export Damaged", "clearance": "Clearance", "ending": "Ending", "importLost": "Import Lost", "exportLost": "Export Lost", "importSellable": "Import Sellable", "exportSellable": "Export Sellable", "sale": "Sales", "total": "Total"}, "purpose": {"clearance": "Clearance", "supplier": "Supplier", "sale": "Sale", "reimport": "Reimport", "stocktaking": "Stocktaking", "transfer": "Transfer", "others": "Others"}, "source_type": "Type", "source": "Sources"}