{"table": {"phone": "Phone number", "customer": "Customer", "care_staff": "Sales reps", "last_care": "Latest care reason", "times": "{{count_times}} times", "source": "Source", "last_care_at": "Latest care", "updated_at": "Lastest update", "created_at": "Creating date", "state": "Data status", "total_price": "Total price", "note": "Note", "collect_type": "Collect type", "reasons": "Number of reason", "load_more": "Loading more data", "refresh_setting": "Reset to default", "shown_columns": "Shown columns", "hide_columns": "Hidden columns", "tag": "Tag", "selected": "{{count}} selected", "origin_project": "Origin project", "product": "Product"}, "action": {"confirm_gather_all_lead": "Confirm to revoke all data?", "confirm_gather_state_lead": "Confirm to revoke {{count}} data in {{state}} state?", "gather_lead_successfully": "Revoke data successfully", "export_data_successfully": "Export data successfully", "gather_lead": "Revoke data", "export_excel": "Download data", "distribution_lead": "Distribute data", "import_excel": "Import data", "gather": {"all": "All", "assigned": "Received", "no_attempt": "Unprocessed", "attempted": "Processed", "potential": "Potential", "failed": "Failed"}, "create_data": "Create data", "duplicate": "Duplicate data", "sort_by_creation_time": "Sort by creation time", "sort_by_reason_update_time": "Sort by reason update time", "sort_by_care_frequency": "Sort by care frequency"}, "filter": {"filter_name_is_empty": "You have not entered a filter name", "saved_filter_successfully": "Save the filter successfully", "an_error": "An error occurred", "saved_filter": "Saved Filter", "manual_filter": "Manual filter", "source": "Source", "select_source": "Select source", "state_and_reason": "Status and reason", "select_state": "Select status", "select_reason": "Select reason", "select_tags": "Select tag", "times": "Times", "numberOfRepeats": "DATA IS REVERSED ≥ X TIMES", "numberOfCares": "NUMBER OF REASON", "timeLastCare": "UPDATE TIME", "filter_guide": "Filter data by selecting an available filter in the list", "removed_filter_successfully": "Delete filter successfully", "confirm_remove_filter": "Confirm to remove filter", "confirm": "Confirm", "cancel": "Cancel", "query": "Search", "type_query": "Enter phone number, customer name", "collect_type": "Collect Method", "select_collect_type": "Select collect type", "include": "Include", "exclude": "Exclude", "and": "And", "or": "Or"}, "common": {"receive": "Receive processing", "successfully": "Operate successfully", "care_staff": "Sales reps", "search_placeholder": "Order code, customer information", "used_to_take_care": "The data I used to care for", "only_me": "My data", "exclude_me": "Data of other people except me", "beginAt": "Start time", "endAt": "End time", "empty_data": "No data", "created_time": "CREATION TIME", "select_marketer": "Select marketer", "select_care_staff": "Select Sales reps", "delete": "Delete", "name_the_filter_and_press_enter_to_save": "Input filter name and press Enter", "apply": "Apply", "save_and_close": "Save", "save_and_apply": "Save and Apply", "appointment_schedule": "Appointment schedule", "all": "All", "select_projects": "Select project", "project": "Project", "facebook_page": "Facebook page", "landing_page": "Landing page", "source": "Source", "latestUpdate": "Latest  update", "latestReason": "Latest care", "clear": "Clear"}, "validate": {"appointment_time_is_required": "Appointment time is required", "content_is_required": "Appointment content is required", "appointment_content": "Appointment content", "send_result": "Send feedback", "please_select_a_care_result": "You need to select the response result"}, "lead": {"mask_failed": "You need to mark this order as not duplicated before replying to the results", "update_care_result": "Feedback result", "select_result": "Select result", "select_tag": "Select tag", "note_for_customer": "More notes about this customer", "care_histories": "Care history", "caring_staff": "Sales reps", "schedule": "Appointment schedule", "duplicate": "Duplicate", "remove": "Delete", "create": "Create", "cancel": "Cancel", "called": "Called", "missed": "Missed", "created_appointment_schedule_successfully": "Create appointment successfully", "removed_appointment_schedule_successfully": "Delete appointment successfully", "mark_as_called_successfully": "Mark as called successfully", "you_are_sure_remove_appointment_schedule": "Are you sure you want to delete the appointment", "Close_the_lead": "Close the lead", "Does_the_customer_have_a_delivery_date": "Does the customer have a delivery date?", "Customers_scheduled_delivery_date": "Customer scheduled delivery date", "Customers_want_to_receive_their_orders_immediately": "Customer wants to receive goods immediately ", "Close_orders_now": "Closing orders now", "Create_new_reminder_to_remind_the_delivery_date_or_call_back_the_customer": "Make an appointment to remind the order delivery date or contact the customer again", "The_preferred_receiving_date": "Desired date of receipt", "Select_date": "Select date", "The_scheduled_call-back_date": "Call appointment date ", "Scheduled_time_24_hours": "Appointment time (24 hours) ", "Select_time": "Select time", "Notes_Required": "Appointment content (Required)", "When_pressing_the_button_below": "When pressing the button below, Lead will be closed first, order has not been sent. You need to re-enter this Lead to confirm the order when the delivery date arrives. ", "Create_reminder_and_close_the_order_temporarily.": "Make an appointment and close lead temporarily", "Create_reminder_and_close": "Create reminders and close temporarily. Return to confirm the order when the appointment date arrives.", "Create_reminder_and_close_the_order_temporarily": "Create reminders and close temporarily. Return to confirm the order when the appointment date arrives."}, "history": {"created": "Create", "updated": "Update", "reason": "Reason", "note": "Note", "tag": "Tag", "changed_state": "Change care status", "times": "the {{count_number}} times", "create_schedule": "Create appointment", "mark_called": "<PERSON>", "remove_schedule": "Delete appointment", "person_in_charge": "Sales reps", "status": "Status", "view_schedule_missed": "View {{total}} past appointments", "ignore_duplicate_warning": "Ignore duplicate warning", "remove_duplicate_warning": "Removed data from duplicate ", "with": "with "}, "reason": {"cannot_reach": "Can not call", "mind_changing": "Changing mind", "urgent_problem": "Emergency", "no_money": "Out of money", "duplicate_order": "Duplicate orders", "test_order": "Test order of MKT", "wrong_phone_number_format": "Phone number is wrong format", "product_does_not_meet_expectation": "Product is not as expected", "awaiting_confirmation": "Awaiting confirmation", "wrong_owner": "Not the phone number owner", "price_factor": "Failed due to price", "wrong_number": "Wrong number", "call_back_later": "Busy, call back later", "no_response": "Not answer the phone", "confirmed": "Confirmed", "purchase_elsewhere": "Buy elsewhere", "accidental_purchase": "Customer clicked wrong", "joking": "Customer teases/ Bom", "blank": "Blank"}, "config": {"collect_type": "Prioritize receiving data collected by method", "quota": "<PERSON><PERSON><PERSON>", "Quota_configuration": "Quota configuration", "Specify_quotas_for_the_system_to_automatically_retrieve_or_allocate_data_for_personnel": "Specify quotas for the system to automatically revoke or allocate data for personnel", "Specify_quotas_for_the_system_to_automatically_retrieve_or_allocate_data_for_personnel_for_as": "Specify the standards for the system to automatically recall or automatically change the data status when exceeding the care standards.", "assigned_leads": "For Assigned data", "maxLeads": "Maximum number of data to be kept", "Unlimited": "Unlimited ", "Maximum": "Maximum", "data": "data", "returnLeadsAt": "Revocation Time", "Revoke_at_the_end_of_the_shift": "Revoke at the end of shift", "Revoke_after_a_specific_time_mark": "Revoke after the specific time mark ", "processing_leads": "For In Process data", "The_maximum_number_of_data_can_be_kept": "Maximum number of data to be kept", "Data": "Data", "returnNoAttemptedAt": "Revocation Time (No Attempt)", "returnAttemptedAt": "Revocation Time (Attempted)", "returnPotentialAt": "Revocation Time (Potential)", "Save": "Save", "no_response": "No response", "Busy_call_back_later": "Busy, call back later", "Awaiting_confirmation": "Awaiting confirmation", "times": "Times", "assigned_and_in_progress": "Assigned and In Process - No Attempt", "assigned": "Assigned", "No Attempt": "(No Attempt)", "minute": "Minutes", "failed_leads": "For Failed data", "mark_as_failed_when_tries_reached_n_times": "Data will be marked Failed after trying to care for many times", "mark_as_canceled_when_failure_reached_n_times": "Data will be marked Canceled when it fails many times", "successfully": "Operation successfully", "average_proceed": "Average processing time/data", "min_limit_data": "Minimum amount of data received", "coefficient_new": "New data coefficient after each request (can be cumulative)", "mark_captured_data_as_failed_when_tries_reached_n_times": "Data Capture Form will be marked Failed after trying to care for many times", "for_manual_data": "For Manual Keying data", "for_captured_data": "For Capture Form data", "for_fb_conversion_data": "For Facebook Conversion data", "markPotentialDataAsFailed": "Potential Data will be marked Failed when updating the reason \"Awaiting confirmation\" many times", "no_withdrawal": "No withdrawal", "mark_facebook_conversion_data_as_failed_when_tries_reached_n_times": "Data Facebook Conversion will be marked Failed after trying to care for many times"}, "schedule": {"today": "Today", "upComing": "UpComing", "missed": "Missed", "have_you_called_customer_yet": "Have you called customers yet?", "mark_as_called": "Mark as called", "no_response": "No response", "mask_as_called_successfully": "Mask as called successfully"}, "success": "Changed Collect Method successfully", "Get Data": "Get Data", "product": "Product", "time_updated": "{{time}}", "Chốt đơn": "Close order", "Không nghe máy": "Not answer the phone", "Đang bận, hẹn lại": "Busy, call back later", "Khách sắp chốt": "Awaiting confirmation", "Không thể gọi": "Can not call", "Sai số": "Wrong number", "Không phải chủ nhân thuê bao": "Not the phone number owner", "Thất bại do giá": "Failed due to price", "Đổi ý": "Changing mind", "Mua bên khác": "Buy elsewhere", "Khách bấm nhầm": "Customer clicked wrong", "Khách trêu/ Boom": "Customer teases/ Boom", "Khẩn cấp": "Emergency", "Hết tiền": "Out of money", "Sản phẩm không như mong muốn": "Product is not as expected", "Trùng đơn": "Duplicate orders", "Đơn test của MKT": "Test order of MKT", "Số điện thoại sai định dạng": "Phone number is wrong format", "All": "All", "Created": "Created At", "system": "System", "assignee": "Select Assignee", "page_group": "Page Group", "(In a request for more data, personnel will receive at least ...)": "(In one request for additional data, the minimum personnel will receive is...)", "Call": {"Call_0003": "Are you sure to delete extension? "}, "Created lead": "Created lead (by duplicating {{lead_id}})", "merged_orders": "Merged orders", "duplicate_leads": "Duplicate leads", "Select All": "Select All", "Deselect All": "Deselect All"}